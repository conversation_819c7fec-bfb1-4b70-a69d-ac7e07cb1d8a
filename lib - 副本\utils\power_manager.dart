import 'package:flutter/material.dart';
import 'kiosk_manager.dart';
import '../theme/industrial_theme.dart';

/// 电源管理工具类
/// 提供设备关机和重启功能
class PowerManager {
  static final PowerManager _instance = PowerManager._internal();
  factory PowerManager() => _instance;
  PowerManager._internal();

  static PowerManager get instance => _instance;

  final KioskManager _kioskManager = KioskManager.instance;

  /// 显示电源操作确认对话框
  Future<bool> showPowerActionDialog(
    BuildContext context, {
    required String title,
    required String message,
    required String actionText,
    required VoidCallback onConfirm,
  }) async {
    return await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          title: Row(
            children: [
              Icon(
                Icons.warning_amber_rounded,
                color: IndustrialTheme.warningAmber,
                size: 28,
              ),
              const SizedBox(width: 12),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF2C3E50),
                ),
              ),
            ],
          ),
          content: Text(
            message,
            style: const TextStyle(
              fontSize: 16,
              color: Color(0xFF34495E),
              height: 1.5,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              style: TextButton.styleFrom(
                foregroundColor: Colors.grey[600],
                padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              ),
              child: const Text(
                '取消',
                style: TextStyle(fontSize: 16),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop(true);
                onConfirm();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: IndustrialTheme.errorRed,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                actionText,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        );
      },
    ) ?? false;
  }

  /// 关机设备
  Future<void> shutdownDevice(BuildContext context) async {
    await showPowerActionDialog(
      context,
      title: '确认关机',
      message: '您确定要关闭设备吗？\n\n关机后需要手动按电源键重新启动设备。',
      actionText: '关机',
      onConfirm: () async {
        try {
          // 显示关机进度提示
          _showPowerActionProgress(context, '正在关机...', Icons.power_settings_new);

          // 执行关机
          await _kioskManager.shutdownDevice();
        } catch (e) {
          if (context.mounted) {
            Navigator.of(context).pop(); // 关闭进度对话框
            _showErrorMessage(context, '关机失败: $e');
          }
        }
      },
    );
  }

  /// 重启设备
  Future<void> rebootDevice(BuildContext context) async {
    await showPowerActionDialog(
      context,
      title: '确认重启',
      message: '您确定要重启设备吗？\n\n重启过程大约需要1-2分钟，请耐心等待。',
      actionText: '重启',
      onConfirm: () async {
        try {
          // 显示重启进度提示
          _showPowerActionProgress(context, '正在重启...', Icons.refresh);

          // 执行重启
          await _kioskManager.rebootDevice();
        } catch (e) {
          if (context.mounted) {
            Navigator.of(context).pop(); // 关闭进度对话框
            _showErrorMessage(context, '重启失败: $e');
          }
        }
      },
    );
  }

  /// 显示电源操作进度对话框
  void _showPowerActionProgress(BuildContext context, String message, IconData icon) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return PopScope(
          canPop: false,
          child: AlertDialog(
            backgroundColor: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const SizedBox(height: 20),
                Icon(
                  icon,
                  size: 48,
                  color: IndustrialTheme.primaryBlue,
                ),
                const SizedBox(height: 24),
                const CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(IndustrialTheme.primaryBlue),
                ),
                const SizedBox(height: 24),
                Text(
                  message,
                  style: const TextStyle(
                    fontSize: 16,
                    color: Color(0xFF2C3E50),
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 20),
              ],
            ),
          ),
        );
      },
    );
  }

  /// 显示错误消息
  void _showErrorMessage(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error_outline, color: Colors.white),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                message,
                style: const TextStyle(fontSize: 16),
              ),
            ),
          ],
        ),
        backgroundColor: IndustrialTheme.errorRed,
        duration: const Duration(seconds: 4),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }
}
