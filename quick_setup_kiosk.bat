@echo off
echo ========================================
echo LYA-1000 Kiosk模式快速设置脚本
echo ========================================
echo.

REM 检查ADB连接
echo 1. 检查设备连接...
adb devices
if %ERRORLEVEL% neq 0 (
    echo 错误: ADB未找到或设备未连接
    echo 请确保:
    echo - 已安装Android SDK Platform Tools
    echo - 设备已连接并启用USB调试
    pause
    exit /b 1
)

echo.
echo 2. 安装应用...
adb install -r build\app\outputs\flutter-apk\app-debug.apk
if %ERRORLEVEL% neq 0 (
    echo 错误: 应用安装失败
    pause
    exit /b 1
)

echo.
echo 3. 设置设备所有者（可选，用于避免系统提示）...
echo 注意: 此步骤需要在全新设备或恢复出厂设置后的设备上执行
echo.
set /p choice="是否设置设备所有者模式? (y/n): "
if /i "%choice%"=="y" (
    echo 正在设置设备所有者...
    adb shell dpm set-device-owner com.example.a325_lya_1000/.DeviceAdminReceiver
    if %ERRORLEVEL% equ 0 (
        echo ✅ 设备所有者设置成功！
        echo 应用将以最高权限运行，无系统提示。
    ) else (
        echo ❌ 设备所有者设置失败
        echo 可能原因:
        echo - 设备上已有用户账户
        echo - 设备已有其他设备所有者
        echo - 需要恢复出厂设置后重试
    )
) else (
    echo 跳过设备所有者设置
    echo 应用将以标准设备管理员模式运行
)

echo.
echo 4. 验证设置...
echo 检查应用安装状态:
adb shell pm list packages | findstr com.example.a325_lya_1000
if %ERRORLEVEL% equ 0 (
    echo ✅ 应用已安装
) else (
    echo ❌ 应用未找到
)

echo.
echo 检查设备所有者状态:
adb shell dpm list-owners | findstr com.example.a325_lya_1000
if %ERRORLEVEL% equ 0 (
    echo ✅ 设备所有者已设置
) else (
    echo ℹ️ 未设置设备所有者（将使用标准模式）
)

echo.
echo 5. 启动应用...
adb shell am start -n com.example.a325_lya_1000/.MainActivity
if %ERRORLEVEL% equ 0 (
    echo ✅ 应用启动成功
) else (
    echo ❌ 应用启动失败
)

echo.
echo ========================================
echo 设置完成！
echo ========================================
echo.
echo 管理员信息:
echo - 退出手势: 连续点击屏幕7次
echo - 管理员密码: LYA1000ADMIN
echo.
echo 如果看到"App is pinned"提示:
echo - 点击"GOT IT"按钮
echo - 或参考setup_device_owner.md设置设备所有者模式
echo.
pause
