# 电源管理功能说明

## 功能概述

在主页右上角添加了电源管理下拉菜单，提供设备关机和重启功能。该功能专为工业应用设计，具有完善的确认机制和错误处理。

## 功能特性

### 1. 用户界面
- **位置**: 主页AppBar右上角，位于系统设置图标旁边
- **图标**: 电源按钮图标 (Icons.power_settings_new)
- **下拉菜单**: 包含"重启设备"和"关机"两个选项
- **工业化设计**: 符合应用整体的工业风格主题

### 2. 安全确认机制
- **双重确认**: 点击菜单项后显示确认对话框
- **详细说明**: 提供操作后果的明确说明
- **取消选项**: 用户可以随时取消操作
- **进度提示**: 执行操作时显示进度对话框

### 3. 权限要求
- **设备管理员权限**: 优先使用设备所有者权限执行操作
- **Root权限**: 作为备用方案使用系统命令
- **系统Intent**: 最后备用方案发送系统广播

## 技术实现

### 1. Android端实现
- **MainActivity.kt**: 添加了`shutdownDevice()`和`rebootDevice()`方法
- **权限配置**: 在AndroidManifest.xml中添加了电源管理相关权限
- **设备管理员**: 在device_admin.xml中添加了重启设备策略

### 2. Flutter端实现
- **KioskManager**: 添加了关机和重启的方法调用
- **PowerManager**: 新增的电源管理工具类，处理UI交互和确认逻辑
- **主页UI**: 在AppBar中添加了电源管理下拉菜单

### 3. 文件修改清单
```
android/app/src/main/kotlin/com/example/a325_lya_1000/MainActivity.kt
android/app/src/main/AndroidManifest.xml
android/app/src/main/res/xml/device_admin.xml
lib/utils/kiosk_manager.dart
lib/utils/power_manager.dart (新增)
lib/main.dart
```

## 使用方法

### 1. 重启设备
1. 点击主页右上角的电源图标
2. 选择"重启设备"
3. 在确认对话框中点击"重启"
4. 等待设备重启完成（约1-2分钟）

### 2. 关机设备
1. 点击主页右上角的电源图标
2. 选择"关机"
3. 在确认对话框中点击"关机"
4. 设备将关闭，需要手动按电源键重新启动

## 错误处理

### 1. 权限不足
- 如果设备管理员权限不足，会尝试使用系统命令
- 如果系统命令失败，会发送系统Intent作为最后尝试
- 所有失败情况都会显示错误提示

### 2. 操作失败
- 显示具体的错误信息
- 自动关闭进度对话框
- 用户可以重新尝试操作

## 安全考虑

### 1. 确认机制
- 防止误操作导致的设备关机或重启
- 清晰的操作说明和后果提示

### 2. 权限控制
- 仅在具有适当权限时执行操作
- 多层级的权限检查和降级处理

### 3. 工业环境适配
- 考虑到工业设备的稳定性要求
- 提供可靠的电源管理功能

## 注意事项

1. **设备所有者权限**: 为了获得最佳的电源管理体验，建议将应用设置为设备所有者
2. **网络连接**: 关机前请确保重要数据已保存
3. **重启时间**: 设备重启可能需要1-2分钟，请耐心等待
4. **工业环境**: 该功能专为工业环境设计，适合无人值守的设备管理

## 测试建议

1. **功能测试**: 在测试环境中验证重启和关机功能
2. **权限测试**: 测试不同权限级别下的功能表现
3. **UI测试**: 验证下拉菜单和确认对话框的显示效果
4. **错误测试**: 模拟权限不足等错误情况
