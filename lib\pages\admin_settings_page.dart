import 'package:flutter/material.dart';
import '../utils/kiosk_manager.dart';
import '../theme/industrial_theme.dart';

/// 管理员设置页面
/// 提供kiosk模式的管理功能
class AdminSettingsPage extends StatefulWidget {
  const AdminSettingsPage({Key? key}) : super(key: key);

  @override
  State<AdminSettingsPage> createState() => _AdminSettingsPageState();
}

class _AdminSettingsPageState extends State<AdminSettingsPage> {
  final KioskManager _kioskManager = KioskManager.instance;
  bool _isKioskModeEnabled = false;
  bool _isDeviceAdminEnabled = false;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadStatus();
  }

  Future<void> _loadStatus() async {
    setState(() => _isLoading = true);
    
    try {
      final kioskStatus = await _kioskManager.checkKioskModeStatus();
      final adminStatus = await _kioskManager.isDeviceAdminEnabled();
      
      setState(() {
        _isKioskModeEnabled = kioskStatus;
        _isDeviceAdminEnabled = adminStatus;
      });
    } catch (e) {
      debugPrint('加载状态失败: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _toggleKioskMode() async {
    setState(() => _isLoading = true);
    
    try {
      bool success;
      if (_isKioskModeEnabled) {
        success = await _kioskManager.disableKioskMode();
        if (success) {
          await _kioskManager.stopLockTask();
        }
      } else {
        success = await _kioskManager.enableKioskMode();
        if (success) {
          await _kioskManager.startLockTask();
        }
      }
      
      if (success) {
        setState(() {
          _isKioskModeEnabled = !_isKioskModeEnabled;
        });
        
        _showMessage(
          _isKioskModeEnabled ? 'Kiosk模式已启用' : 'Kiosk模式已禁用',
          Colors.green,
        );
      } else {
        _showMessage('操作失败', Colors.red);
      }
    } catch (e) {
      _showMessage('操作失败: $e', Colors.red);
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _toggleDeviceAdmin() async {
    setState(() => _isLoading = true);
    
    try {
      bool success;
      if (_isDeviceAdminEnabled) {
        success = await _kioskManager.disableDeviceAdmin();
      } else {
        success = await _kioskManager.enableDeviceAdmin();
      }
      
      // 重新检查状态
      await Future.delayed(const Duration(seconds: 1));
      final newStatus = await _kioskManager.isDeviceAdminEnabled();
      
      setState(() {
        _isDeviceAdminEnabled = newStatus;
      });
      
      _showMessage(
        _isDeviceAdminEnabled ? '设备管理员已启用' : '设备管理员已禁用',
        Colors.green,
      );
    } catch (e) {
      _showMessage('操作失败: $e', Colors.red);
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _showMessage(String message, Color color) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: color,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('管理员设置'),
        backgroundColor: IndustrialTheme.primaryBlue,
        foregroundColor: Colors.white,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Kiosk模式管理',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: IndustrialTheme.textPrimary,
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    '管理应用的kiosk模式设置和权限',
                    style: TextStyle(
                      fontSize: 16,
                      color: IndustrialTheme.textSecondary,
                    ),
                  ),
                  const SizedBox(height: 32),
                  
                  // Kiosk模式开关
                  Card(
                    child: ListTile(
                      leading: Icon(
                        _isKioskModeEnabled ? Icons.lock : Icons.lock_open,
                        color: _isKioskModeEnabled 
                            ? IndustrialTheme.successGreen 
                            : IndustrialTheme.warningAmber,
                      ),
                      title: const Text('Kiosk模式'),
                      subtitle: Text(
                        _isKioskModeEnabled 
                            ? '已启用 - 应用处于锁定状态' 
                            : '已禁用 - 用户可以退出应用',
                      ),
                      trailing: Switch(
                        value: _isKioskModeEnabled,
                        onChanged: _isLoading ? null : (_) => _toggleKioskMode(),
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // 设备管理员开关
                  Card(
                    child: ListTile(
                      leading: Icon(
                        _isDeviceAdminEnabled ? Icons.admin_panel_settings : Icons.security,
                        color: _isDeviceAdminEnabled 
                            ? IndustrialTheme.successGreen 
                            : IndustrialTheme.warningAmber,
                      ),
                      title: const Text('设备管理员'),
                      subtitle: Text(
                        _isDeviceAdminEnabled 
                            ? '已启用 - 具有设备管理权限' 
                            : '已禁用 - 无设备管理权限',
                      ),
                      trailing: Switch(
                        value: _isDeviceAdminEnabled,
                        onChanged: _isLoading ? null : (_) => _toggleDeviceAdmin(),
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 32),
                  
                  // 操作按钮
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: _isLoading ? null : _loadStatus,
                          icon: const Icon(Icons.refresh),
                          label: const Text('刷新状态'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: IndustrialTheme.primaryBlue,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 12),
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () => Navigator.of(context).pop(),
                          icon: const Icon(Icons.arrow_back),
                          label: const Text('返回'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: IndustrialTheme.borderGrey,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 12),
                          ),
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 32),
                  
                  // 说明信息
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: IndustrialTheme.primaryBlue.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: IndustrialTheme.primaryBlue.withOpacity(0.3),
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: const [
                        Text(
                          '使用说明：',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: IndustrialTheme.textPrimary,
                          ),
                        ),
                        SizedBox(height: 8),
                        Text(
                          '• Kiosk模式：启用后应用将全屏运行，用户无法退出\n'
                          '• 设备管理员：提供更高级的设备控制权限\n'
                          '• 管理员密码：LYA1000ADMIN\n'
                          '• 退出手势：连续点击屏幕7次',
                          style: TextStyle(
                            color: IndustrialTheme.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
    );
  }
}
