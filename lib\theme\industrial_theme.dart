import 'package:flutter/material.dart';

/// 工业级专业主题设计
/// 面向管道检测等工业应用的专业UI主题
class IndustrialTheme {
  // 工业级色彩方案
  static const Color primaryBlue = Color(0xFF1565C0);      // 主蓝色 - 专业可靠
  static const Color secondaryBlue = Color(0xFF0D47A1);    // 深蓝色 - 权威稳重
  static const Color accentOrange = Color(0xFFFF6F00);     // 橙色 - 警示重要
  static const Color successGreen = Color(0xFF2E7D32);     // 绿色 - 成功状态
  static const Color warningAmber = Color(0xFFE65100);     // 琥珀色 - 警告
  static const Color errorRed = Color(0xFFD32F2F);         // 红色 - 错误危险
  
  // 中性色系
  static const Color surfaceGrey = Color(0xFFF5F7FA);      // 浅灰背景
  static const Color cardGrey = Color(0xFFFFFFFF);         // 卡片白色
  static const Color borderGrey = Color(0xFFE1E5E9);       // 边框灰色
  static const Color textPrimary = Color(0xFF1A1A1A);      // 主文字
  static const Color textSecondary = Color(0xFF6B7280);    // 次要文字
  static const Color textDisabled = Color(0xFF9CA3AF);     // 禁用文字
  
  // 功能色彩
  static const Color dataBlue = Color(0xFF1976D2);         // 数据相关
  static const Color gpsGreen = Color(0xFF388E3C);         // GPS定位
  static const Color cameraIndigo = Color(0xFF3F51B5);     // 相机拍照
  static const Color processingPurple = Color(0xFF7B1FA2); // 数据处理

  /// 获取工业主题配置
  static ThemeData getIndustrialTheme() {
    return ThemeData(
      useMaterial3: true,
      fontFamily: 'Roboto',
      
      // 色彩方案
      colorScheme: const ColorScheme.light(
        primary: primaryBlue,
        secondary: accentOrange,
        surface: surfaceGrey,
        background: surfaceGrey,
        error: errorRed,
        onPrimary: Colors.white,
        onSecondary: Colors.white,
        onSurface: textPrimary,
        onBackground: textPrimary,
        onError: Colors.white,
      ),
      
      // AppBar主题
      appBarTheme: const AppBarTheme(
        backgroundColor: primaryBlue,
        foregroundColor: Colors.white,
        elevation: 2,
        centerTitle: false,
        titleTextStyle: TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: Colors.white,
          letterSpacing: 0.5,
        ),
        iconTheme: IconThemeData(color: Colors.white, size: 24),
      ),
      
      // 卡片主题
      cardTheme: CardThemeData(
        color: cardGrey,
        elevation: 3,
        shadowColor: Colors.black.withValues(alpha: 0.1),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
          side: BorderSide(color: borderGrey, width: 1),
        ),
        margin: const EdgeInsets.all(8),
      ),
      
      // 按钮主题
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: primaryBlue,
          foregroundColor: Colors.white,
          elevation: 2,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          textStyle: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            letterSpacing: 0.5,
          ),
        ),
      ),
      
      // 输入框主题
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: Colors.white,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: borderGrey),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: borderGrey),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: primaryBlue, width: 2),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        labelStyle: TextStyle(color: textSecondary),
        hintStyle: TextStyle(color: textDisabled),
      ),
      
      // 文字主题
      textTheme: const TextTheme(
        headlineLarge: TextStyle(
          fontSize: 28,
          fontWeight: FontWeight.bold,
          color: textPrimary,
          letterSpacing: 0.5,
        ),
        headlineMedium: TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.w600,
          color: textPrimary,
          letterSpacing: 0.5,
        ),
        titleLarge: TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: textPrimary,
          letterSpacing: 0.5,
        ),
        titleMedium: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w500,
          color: textPrimary,
          letterSpacing: 0.25,
        ),
        bodyLarge: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.normal,
          color: textPrimary,
          letterSpacing: 0.25,
        ),
        bodyMedium: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.normal,
          color: textSecondary,
          letterSpacing: 0.25,
        ),
        labelLarge: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w600,
          color: textPrimary,
          letterSpacing: 0.5,
        ),
      ),
    );
  }
  
  /// 获取状态颜色
  static Color getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'success':
      case '成功':
      case '完成':
        return successGreen;
      case 'warning':
      case '警告':
      case '注意':
        return warningAmber;
      case 'error':
      case '错误':
      case '失败':
        return errorRed;
      case 'processing':
      case '处理中':
      case '进行中':
        return processingPurple;
      default:
        return primaryBlue;
    }
  }
  
  /// 获取功能模块颜色
  static Color getModuleColor(String module) {
    switch (module.toLowerCase()) {
      case 'data':
      case '数据':
        return dataBlue;
      case 'gps':
      case '定位':
        return gpsGreen;
      case 'camera':
      case '相机':
        return cameraIndigo;
      case 'processing':
      case '处理':
        return processingPurple;
      default:
        return primaryBlue;
    }
  }
}

/// 工业级组件样式
class IndustrialStyles {
  // 阴影样式
  static List<BoxShadow> get cardShadow => [
    BoxShadow(
      color: Colors.black.withValues(alpha: 0.08),
      blurRadius: 8,
      offset: const Offset(0, 2),
    ),
  ];

  static List<BoxShadow> get elevatedShadow => [
    BoxShadow(
      color: Colors.black.withValues(alpha: 0.12),
      blurRadius: 12,
      offset: const Offset(0, 4),
    ),
  ];
  
  // 边框样式
  static BorderRadius get cardRadius => BorderRadius.circular(12);
  static BorderRadius get buttonRadius => BorderRadius.circular(8);
  static BorderRadius get inputRadius => BorderRadius.circular(8);
  
  // 间距常量
  static const double spacingXS = 4;
  static const double spacingS = 8;
  static const double spacingM = 16;
  static const double spacingL = 24;
  static const double spacingXL = 32;
  
  // 图标大小
  static const double iconS = 16;
  static const double iconM = 24;
  static const double iconL = 32;
  static const double iconXL = 48;
}
