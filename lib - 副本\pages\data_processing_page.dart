import 'package:flutter/material.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'dart:async';

class DataProcessingPage extends StatefulWidget {
  final String projectUniqueId;
  final String projectName;

  const DataProcessingPage({
    Key? key,
    required this.projectUniqueId,
    required this.projectName,
  }) : super(key: key);

  @override
  State<DataProcessingPage> createState() => _DataProcessingPageState();
}

class _DataProcessingPageState extends State<DataProcessingPage> {
  late Database _db;
  List<Map<String, dynamic>> _dataRows = [];
  bool _loading = true;
  final ScrollController _verticalScrollController = ScrollController();
  final ScrollController _headerScrollController = ScrollController();
  final ScrollController _bodyScrollController = ScrollController();

  // 编辑状态管理
  Map<String, TextEditingController> _controllers = {};
  Map<String, FocusNode> _focusNodes = {};
  Map<String, Timer?> _saveTimers = {};

  // 表字段名和显示名映射（增加原始图片路径字段）
  final List<String> _fieldKeys = [
    'id',
    'distance',
    'mileage',
    'current',
    'depth',
    'db',
    'description',
    'latitude',
    'longitude',
    'altitude',
    'original_image_path',
  ];

  final List<String> _fieldLabels = [
    '序号',
    '距离(m)',
    '里程',
    '电流(mA)',
    '埋深(m)',
    'DB',
    '描述',
    '北纬',
    '东经',
    '高度',
    '原始图片',
  ];

  // 字段宽度配置
  final Map<String, double> _fieldWidths = {
    'id': 80,
    'distance': 100,
    'mileage': 120,
    'current': 100,
    'depth': 100,
    'db': 80,
    'description': 150,
    'latitude': 120,
    'longitude': 120,
    'altitude': 100,
    'original_image_path': 200,
  };

  @override
  void initState() {
    super.initState();
    _initDatabase();
    _setupScrollSync();
  }

  void _setupScrollSync() {
    // 监听表格内容的水平滚动，同步表头滚动
    _bodyScrollController.addListener(() {
      if (_headerScrollController.hasClients && _bodyScrollController.hasClients) {
        if (_headerScrollController.offset != _bodyScrollController.offset) {
          _headerScrollController.jumpTo(_bodyScrollController.offset);
        }
      }
    });

    // 监听表头的水平滚动，同步表格内容滚动
    _headerScrollController.addListener(() {
      if (_headerScrollController.hasClients && _bodyScrollController.hasClients) {
        if (_bodyScrollController.offset != _headerScrollController.offset) {
          _bodyScrollController.jumpTo(_headerScrollController.offset);
        }
      }
    });
  }

  @override
  void dispose() {
    // 清理控制器和定时器
    for (final controller in _controllers.values) {
      controller.dispose();
    }
    for (final node in _focusNodes.values) {
      node.dispose();
    }
    for (final timer in _saveTimers.values) {
      timer?.cancel();
    }
    _verticalScrollController.dispose();
    _headerScrollController.dispose();
    _bodyScrollController.dispose();
    super.dispose();
  }

  Future<void> _initDatabase() async {
    final dbPath = await getDatabasesPath();
    _db = await openDatabase(
      join(dbPath, 'data_processing.db'),
      version: 2, // 增加版本号以支持新字段
      onCreate: (db, version) async {
        // 不创建表，表由项目uniqueId动态创建
      },
      onUpgrade: (db, oldVersion, newVersion) async {
        if (oldVersion < 2) {
          // 升级数据库，添加原始图片路径字段
          try {
            final tableName = widget.projectUniqueId;
            await db.execute('ALTER TABLE `$tableName` ADD COLUMN original_image_path TEXT');
          } catch (e) {
            // 数据库升级失败，忽略错误继续执行
          }
        }
      },
    );
    await _createTableIfNotExists();
    await _loadData();
  }

  Future<void> _createTableIfNotExists() async {
    final tableName = widget.projectUniqueId;
    // 用反引号包裹表名，避免特殊字符导致的语法错误
    await _db.execute('''
      CREATE TABLE IF NOT EXISTS `$tableName` (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        distance REAL,
        mileage TEXT,
        current REAL,
        depth REAL,
        db REAL,
        description TEXT,
        latitude REAL,
        longitude REAL,
        altitude REAL,
        original_image_path TEXT
      )
    ''');
  }

  Future<void> _loadData() async {
    final tableName = widget.projectUniqueId;
    final List<Map<String, dynamic>> rows = await _db.query('`$tableName`', orderBy: 'id ASC');
    setState(() {
      _dataRows = rows;
      _loading = false;
    });
    _initializeControllers();
  }

  void _initializeControllers() {
    // 清理旧的控制器
    for (final controller in _controllers.values) {
      controller.dispose();
    }
    for (final node in _focusNodes.values) {
      node.dispose();
    }
    _controllers.clear();
    _focusNodes.clear();

    // 为每个可编辑字段创建控制器
    for (int rowIndex = 0; rowIndex < _dataRows.length; rowIndex++) {
      final row = _dataRows[rowIndex];
      for (final field in _fieldKeys) {
        if (field != 'id') { // ID字段不可编辑
          final key = '${row['id']}_$field';
          _controllers[key] = TextEditingController(text: row[field]?.toString() ?? '');
          _focusNodes[key] = FocusNode();

          // 添加失焦监听器，实现自动保存
          _focusNodes[key]!.addListener(() {
            if (!_focusNodes[key]!.hasFocus) {
              _scheduleAutoSave(row['id'], field, _controllers[key]!.text);
            }
          });
        }
      }
    }
  }

  void _scheduleAutoSave(int id, String field, String value) {
    final key = '${id}_$field';

    // 取消之前的定时器
    _saveTimers[key]?.cancel();

    // 设置新的定时器，延迟保存
    _saveTimers[key] = Timer(const Duration(milliseconds: 500), () {
      _autoSaveField(id, field, value);
    });
  }

  Future<void> _autoSaveField(int id, String field, String value) async {
    try {
      dynamic parsedValue = value;

      // 根据字段类型转换值
      if (['distance', 'current', 'depth', 'db', 'latitude', 'longitude', 'altitude'].contains(field)) {
        parsedValue = double.tryParse(value) ?? 0.0;
      }

      final tableName = widget.projectUniqueId;
      await _db.update(
        '`$tableName`',
        {field: parsedValue},
        where: 'id = ?',
        whereArgs: [id],
      );

      // 更新本地数据
      final rowIndex = _dataRows.indexWhere((row) => row['id'] == id);
      if (rowIndex != -1) {
        setState(() {
          _dataRows[rowIndex][field] = parsedValue;
        });
      }

      // 自动保存成功，无需显示提示以避免干扰用户
    } catch (e) {
      // 保存失败，静默处理
    }
  }

  Future<void> _insertRow() async {
    final tableName = widget.projectUniqueId;
    await _db.insert('`$tableName`', {
      'distance': 0.0,
      'mileage': '',
      'current': 0.0,
      'depth': 0.0,
      'db': 0.0,
      'description': '',
      'latitude': 0.0,
      'longitude': 0.0,
      'altitude': 0.0,
      'original_image_path': '',
    });
    await _loadData();
  }

  Future<void> _deleteRow(int id) async {
    final tableName = widget.projectUniqueId;
    await _db.delete(
      '`$tableName`',
      where: 'id = ?',
      whereArgs: [id],
    );
    await _loadData();
  }

  Widget _buildCell(int rowIndex, String field, dynamic value, bool isId) {
    if (isId) {
      return Container(
        width: _fieldWidths[field],
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
        child: Text(
          value.toString(),
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Color(0xFF2C3E50),
          ),
        ),
      );
    }

    final row = _dataRows[rowIndex];
    final key = '${row['id']}_$field';
    final controller = _controllers[key];
    final focusNode = _focusNodes[key];

    if (controller == null || focusNode == null) {
      return Container(
        width: _fieldWidths[field],
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
        child: Text(value?.toString() ?? ''),
      );
    }

    return Container(
      width: _fieldWidths[field],
      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 4),
      child: TextField(
        controller: controller,
        focusNode: focusNode,
        style: const TextStyle(
          fontSize: 13,
          color: Color(0xFF2C3E50),
        ),
        decoration: InputDecoration(
          isDense: true,
          contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(4),
            borderSide: const BorderSide(color: Color(0xFFBDC3C7), width: 1),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(4),
            borderSide: const BorderSide(color: Color(0xFFBDC3C7), width: 1),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(4),
            borderSide: const BorderSide(color: Color(0xFF3498DB), width: 2),
          ),
          filled: true,
          fillColor: Colors.white,
        ),
        onChanged: (value) {
          // 实时更新，但延迟保存
          _scheduleAutoSave(row['id'], field, value);
        },
      ),
    );
  }

  Widget _buildIndustrialLoadingIndicator() {
    return Container(
      color: const Color(0xFFF8F9FA),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: const Color(0xFF34495E),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Center(
                child: SizedBox(
                  width: 30,
                  height: 30,
                  child: CircularProgressIndicator(
                    strokeWidth: 3,
                    valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF3498DB)),
                  ),
                ),
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              '正在加载数据...',
              style: TextStyle(
                fontSize: 16,
                color: Color(0xFF7F8C8D),
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final double screenWidth = MediaQuery.of(context).size.width;

    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        title: Text(
          '数据处理 - ${widget.projectName}',
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF2C3E50),
        elevation: 2,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          Container(
            margin: const EdgeInsets.only(right: 8),
            child: IconButton(
              icon: const Icon(Icons.add_circle_outline, size: 24),
              tooltip: '新增数据行',
              onPressed: _insertRow,
              style: IconButton.styleFrom(
                backgroundColor: const Color(0xFF3498DB),
                foregroundColor: Colors.white,
              ),
            ),
          ),
        ],
      ),
      body: _loading
          ? _buildIndustrialLoadingIndicator()
          : _buildDataTable(context, screenWidth),
    );
  }

  Widget _buildDataTable(BuildContext context, double screenWidth) {
    // 计算表格总宽度
    double totalWidth = _fieldWidths.values.reduce((a, b) => a + b) + (_fieldKeys.length * 12);

    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // 固定表头
          _buildTableHeader(totalWidth),
          // 数据行区域
          Expanded(
            child: _buildTableBody(context, totalWidth),
          ),
        ],
      ),
    );
  }

  Widget _buildTableHeader(double totalWidth) {
    return Container(
      width: totalWidth,
      height: 50,
      decoration: const BoxDecoration(
        color: Color(0xFF34495E),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(8),
          topRight: Radius.circular(8),
        ),
      ),
      child: SingleChildScrollView(
        controller: _headerScrollController,
        scrollDirection: Axis.horizontal,
        child: Row(
          children: List.generate(_fieldKeys.length, (index) {
            final field = _fieldKeys[index];
            final label = _fieldLabels[index];
            return Container(
              width: _fieldWidths[field],
              height: 50,
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                border: Border(
                  right: index < _fieldKeys.length - 1
                      ? const BorderSide(color: Color(0xFF2C3E50), width: 1)
                      : BorderSide.none,
                ),
              ),
              child: Center(
                child: Text(
                  label,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            );
          }),
        ),
      ),
    );
  }

  Widget _buildTableBody(BuildContext context, double totalWidth) {
    return SingleChildScrollView(
      controller: _verticalScrollController,
      child: SingleChildScrollView(
        controller: _bodyScrollController,
        scrollDirection: Axis.horizontal,
        child: Column(
          children: List.generate(_dataRows.length, (rowIndex) {
            final row = _dataRows[rowIndex];
            return _buildDataRow(context, row, rowIndex, totalWidth);
          }),
        ),
      ),
    );
  }

  Widget _buildDataRow(BuildContext context, Map<String, dynamic> row, int rowIndex, double totalWidth) {
    return Container(
      width: totalWidth,
      decoration: BoxDecoration(
        color: rowIndex % 2 == 0 ? Colors.white : const Color(0xFFF8F9FA),
        border: const Border(
          bottom: BorderSide(color: Color(0xFFE9ECEF), width: 1),
        ),
      ),
      child: InkWell(
        onLongPress: () => _showDeleteDialog(context, row['id']),
        child: Row(
          children: List.generate(_fieldKeys.length, (colIndex) {
            final field = _fieldKeys[colIndex];
            final isId = field == 'id';
            return Container(
              width: _fieldWidths[field],
              decoration: BoxDecoration(
                border: Border(
                  right: colIndex < _fieldKeys.length - 1
                      ? const BorderSide(color: Color(0xFFE9ECEF), width: 1)
                      : BorderSide.none,
                ),
              ),
              child: _buildCell(rowIndex, field, row[field], isId),
            );
          }),
        ),
      ),
    );
  }

  void _showDeleteDialog(BuildContext context, int id) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        title: const Text(
          '确认删除',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Color(0xFF2C3E50),
          ),
        ),
        content: const Text(
          '确定要删除这条数据吗？此操作不可撤销。',
          style: TextStyle(
            fontSize: 14,
            color: Color(0xFF7F8C8D),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            style: TextButton.styleFrom(
              foregroundColor: const Color(0xFF7F8C8D),
            ),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteRow(id);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFFE74C3C),
              foregroundColor: Colors.white,
            ),
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }
}