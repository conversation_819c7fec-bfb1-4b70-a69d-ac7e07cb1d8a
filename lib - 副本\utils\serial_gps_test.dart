import '../service/serial_gps_service.dart';

/// 串口GPS服务测试工具
class SerialGPSTest {
  static final SerialGPSService _gpsService = SerialGPSService.instance;
  
  /// 测试串口GPS连接和数据读取
  static Future<void> testSerialGPS() async {
    print('=== 开始测试串口GPS服务 ===');
    
    // 添加数据监听器
    _gpsService.addListener((gpsData) {
      print('📍 [GPS数据更新] $gpsData');
      _printFormattedGPSData(gpsData);
    });
    
    try {
      // 尝试连接串口
      print('🔌 正在连接串口GPS模块...');
      final connected = await _gpsService.connect();
      
      if (connected) {
        print('✅ 串口GPS连接成功！');
        print('📡 正在监听GPS数据，按Ctrl+C停止...');
        
        // 持续监听数据（在实际应用中，这会在后台运行）
        await _monitorGPSData();
        
      } else {
        print('❌ 串口GPS连接失败！');
        print('💡 请检查：');
        print('   1. GPS模块是否正确连接到 /dev/ttyUSB3');
        print('   2. 设备权限是否正确');
        print('   3. GPS模块是否正常工作');
      }
      
    } catch (e) {
      print('💥 测试过程中发生错误: $e');
    } finally {
      // 清理资源
      await _gpsService.disconnect();
      print('🔌 串口GPS连接已断开');
    }
    
    print('=== 串口GPS服务测试结束 ===');
  }
  
  /// 监听GPS数据
  static Future<void> _monitorGPSData() async {
    // 模拟持续监听30秒
    await Future.delayed(Duration(seconds: 30));
    
    // 显示最终状态
    final latestData = _gpsService.latestData;
    if (latestData != null) {
      print('\n📊 最终GPS数据:');
      _printFormattedGPSData(latestData);
    } else {
      print('\n⚠️  未接收到有效的GPS数据');
    }
  }
  
  /// 格式化打印GPS数据
  static void _printFormattedGPSData(GPSData gpsData) {
    print('┌─────────────────────────────────────────┐');
    print('│              GPS 数据详情                │');
    print('├─────────────────────────────────────────┤');
    print('│ 纬度: ${gpsData.latitude.toStringAsFixed(8)}°${_getLatitudeDirection(gpsData.latitude)}');
    print('│ 经度: ${gpsData.longitude.toStringAsFixed(8)}°${_getLongitudeDirection(gpsData.longitude)}');
    
    if (gpsData.altitude != null) {
      print('│ 海拔: ${gpsData.altitude!.toStringAsFixed(2)} 米');
    } else {
      print('│ 海拔: 未知');
    }
    
    if (gpsData.fixQuality != null) {
      print('│ 定位质量: ${gpsData.fixQuality}');
    }
    
    if (gpsData.satelliteCount != null) {
      print('│ 卫星数量: ${gpsData.satelliteCount} 颗');
    }
    
    print('│ 更新时间: ${_formatDateTime(gpsData.timestamp)}');
    print('└─────────────────────────────────────────┘');
    
    // 显示坐标链接（可在地图中查看）
    final mapsUrl = 'https://www.google.com/maps?q=${gpsData.latitude},${gpsData.longitude}';
    print('🗺️  Google地图链接: $mapsUrl');
    print('');
  }
  
  /// 获取纬度方向
  static String _getLatitudeDirection(double latitude) {
    return latitude >= 0 ? 'N' : 'S';
  }
  
  /// 获取经度方向
  static String _getLongitudeDirection(double longitude) {
    return longitude >= 0 ? 'E' : 'W';
  }
  
  /// 格式化日期时间
  static String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
           '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}:${dateTime.second.toString().padLeft(2, '0')}';
  }
  
  /// 测试NMEA数据解析（使用模拟数据）
  static void testNMEAParser() {
    print('=== 测试NMEA数据解析 ===');
    
    // 模拟NMEA数据
    final testNMEAData = [
      '\$GPGGA,123519,4807.038,N,01131.000,E,1,08,0.9,545.4,M,46.9,M,,*47',
      '\$GPRMC,123519,A,4807.038,N,01131.000,E,022.4,084.4,230394,003.1,W*6A',
      '\$GPGSV,2,1,08,01,40,083,46,02,17,308,41,12,07,344,39,14,22,228,45*75',
    ];
    
    print('📡 模拟接收NMEA数据:');
    for (final data in testNMEAData) {
      print('   $data');
    }
    
    print('\n💡 在实际使用中，这些数据将从串口 /dev/ttyUSB3 读取');
    print('💡 解析后的GPS信息将实时更新并通过监听器回调');
    
    print('=== NMEA数据解析测试结束 ===');
  }
}

/// 独立的GPS测试函数，可以在main函数中调用
Future<void> runSerialGPSTest() async {
  await SerialGPSTest.testSerialGPS();
}

/// 测试NMEA解析器
void runNMEAParserTest() {
  SerialGPSTest.testNMEAParser();
}
