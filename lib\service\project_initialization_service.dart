import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:uuid/uuid.dart';
import 'package:get_storage/get_storage.dart';
import '../pages/project_management_page.dart';

/// 项目初始化服务
/// 负责检查和创建默认项目
class ProjectInitializationService {
  static final ProjectInitializationService _instance = ProjectInitializationService._internal();
  factory ProjectInitializationService() => _instance;
  ProjectInitializationService._internal();

  static ProjectInitializationService get instance => _instance;

  final GetStorage _storage = GetStorage();
  final Uuid _uuid = const Uuid();

  // 存储键
  static const String _hasInitializedKey = 'has_initialized_default_project';
  static const String _defaultProjectCreatedKey = 'default_project_created';

  /// 检查并初始化默认项目
  Future<void> checkAndInitializeDefaultProject() async {
    try {
      // 检查是否已经初始化过
      final hasInitialized = _storage.read(_hasInitializedKey) ?? false;
      
      if (hasInitialized) {
        print('[项目初始化] 已经初始化过，跳过默认项目创建');
        return;
      }

      // 检查数据库中是否已有项目
      final hasExistingProjects = await _hasExistingProjects();
      
      if (hasExistingProjects) {
        print('[项目初始化] 数据库中已有项目，跳过默认项目创建');
        _storage.write(_hasInitializedKey, true);
        return;
      }

      // 创建默认项目
      await _createDefaultProject();
      
      // 标记已初始化
      _storage.write(_hasInitializedKey, true);
      _storage.write(_defaultProjectCreatedKey, true);
      
      print('[项目初始化] 默认项目创建完成');
      
    } catch (e) {
      print('[项目初始化] 初始化失败: $e');
    }
  }

  /// 检查数据库中是否已有项目
  Future<bool> _hasExistingProjects() async {
    try {
      final Database db = await openDatabase(
        join(await getDatabasesPath(), 'projects_database_0707.db'),
        onCreate: (db, version) {
          return db.execute('''
            CREATE TABLE projects(
              id INTEGER PRIMARY KEY, 
              uniqueId TEXT UNIQUE,
              name TEXT, 
              location TEXT, 
              date TEXT,
              signalEntryPoint TEXT,
              startPoint TEXT,
              diameter TEXT,
              thickness TEXT,
              material TEXT,
              medium TEXT,
              coating TEXT,
              soilResistivity TEXT,
              workingFrequency TEXT,
              initialCurrent TEXT,
              detectionDirection TEXT
            )
            ''');
        },
        version: 1,
      );

      final List<Map<String, dynamic>> maps = await db.query('projects');
      await db.close();
      
      return maps.isNotEmpty;
    } catch (e) {
      print('[项目初始化] 检查现有项目失败: $e');
      return false;
    }
  }

  /// 创建默认项目
  Future<void> _createDefaultProject() async {
    try {
      final defaultProject = Project(
        uniqueId: _uuid.v4(),
        name: '默认项目',
        location: '待设置',
        date: DateTime.now().toString().split(' ')[0], // 当前日期 YYYY-MM-DD
        signalEntryPoint: '待设置',
        startPoint: '待设置',
        diameter: '待设置',
        thickness: '待设置',
        material: '待设置',
        medium: '待设置',
        coating: '待设置',
        soilResistivity: '待设置',
        workingFrequency: '待设置',
        initialCurrent: '待设置',
        detectionDirection: '待设置',
      );

      final Database db = await openDatabase(
        join(await getDatabasesPath(), 'projects_database_0707.db'),
        onCreate: (db, version) {
          return db.execute('''
            CREATE TABLE projects(
              id INTEGER PRIMARY KEY, 
              uniqueId TEXT UNIQUE,
              name TEXT, 
              location TEXT, 
              date TEXT,
              signalEntryPoint TEXT,
              startPoint TEXT,
              diameter TEXT,
              thickness TEXT,
              material TEXT,
              medium TEXT,
              coating TEXT,
              soilResistivity TEXT,
              workingFrequency TEXT,
              initialCurrent TEXT,
              detectionDirection TEXT
            )
            ''');
        },
        version: 1,
      );

      await db.insert(
        'projects',
        defaultProject.toMap(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );

      // 设置为当前项目
      _storage.write('currentProjectName', defaultProject.name);
      _storage.write('currentProjectUniqueId', defaultProject.uniqueId);

      await db.close();
      
      print('[项目初始化] 默认项目 "${defaultProject.name}" 创建成功');
      
    } catch (e) {
      print('[项目初始化] 创建默认项目失败: $e');
      throw e;
    }
  }

  /// 重置初始化状态（用于测试或重新初始化）
  void resetInitializationStatus() {
    _storage.remove(_hasInitializedKey);
    _storage.remove(_defaultProjectCreatedKey);
    print('[项目初始化] 初始化状态已重置');
  }

  /// 检查是否已创建默认项目
  bool get hasCreatedDefaultProject {
    return _storage.read(_defaultProjectCreatedKey) ?? false;
  }

  /// 获取当前项目信息
  Map<String, String?> getCurrentProjectInfo() {
    return {
      'name': _storage.read('currentProjectName'),
      'uniqueId': _storage.read('currentProjectUniqueId'),
    };
  }
}
