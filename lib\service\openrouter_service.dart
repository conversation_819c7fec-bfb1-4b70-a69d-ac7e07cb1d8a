import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:http/http.dart' as http;

/// OpenRouter API服务
class OpenRouterService {
  static const String _baseUrl = 'https://openrouter.ai/api/v1/chat/completions';
  static const String _apiKey = '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';
  
  // 默认模型配置
  static const String defaultVisionModel = 'qwen/qwen2.5-vl-72b-instruct:free';
  static const String defaultChatModel = 'deepseek/deepseek-chat-v3-0324:free';
  
  /// 文本对话接口
  static Future<String> chatCompletion({
    required String message,
    String? model,
    double temperature = 0.7,
    int maxTokens = 1000,
  }) async {
    try {
      final response = await http.post(
        Uri.parse(_baseUrl),
        headers: {
          'Authorization': 'Bearer $_apiKey',
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://your-app.com',
          'X-Title': 'Industrial Data Collection App',
        },
        body: jsonEncode({
          'model': model ?? defaultChatModel,
          'messages': [
            {
              'role': 'user',
              'content': message,
            }
          ],
          'temperature': temperature,
          'max_tokens': maxTokens,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return data['choices'][0]['message']['content'] ?? '';
      } else {
        throw Exception('OpenRouter API请求失败: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      throw Exception('OpenRouter文本对话失败: $e');
    }
  }

  /// 基于图片的对话接口
  static Future<String> visionCompletion({
    required File imageFile,
    required String prompt,
    String? model,
    double temperature = 0.7,
    int maxTokens = 1000,
  }) async {
    try {
      // 将图片转换为base64
      final bytes = await imageFile.readAsBytes();
      final base64Image = base64Encode(bytes);
      final mimeType = _getMimeType(imageFile.path);
      
      final response = await http.post(
        Uri.parse(_baseUrl),
        headers: {
          'Authorization': 'Bearer $_apiKey',
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://your-app.com',
          'X-Title': 'Industrial Data Collection App',
        },
        body: jsonEncode({
          'model': model ?? defaultVisionModel,
          'messages': [
            {
              'role': 'user',
              'content': [
                {
                  'type': 'text',
                  'text': prompt,
                },
                {
                  'type': 'image_url',
                  'image_url': {
                    'url': 'data:$mimeType;base64,$base64Image',
                  },
                },
              ],
            }
          ],
          'temperature': temperature,
          'max_tokens': maxTokens,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return data['choices'][0]['message']['content'] ?? '';
      } else {
        throw Exception('OpenRouter API请求失败: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      throw Exception('OpenRouter视觉对话失败: $e');
    }
  }

  /// OCR数字识别专用接口
  static Future<String> recognizeNumbers(File imageFile, {String? model}) async {
    const prompt = '''
请识别图片中的数字。要求：
1. 只返回识别到的数字，不要其他文字
2. 如果有多个数字，用空格分隔
3. 如果没有识别到数字，返回"-"
4. 专注于仪表、显示屏等设备上的数字
5. 忽略背景中的无关数字

请直接返回数字结果：
''';

    try {
      final result = await visionCompletion(
        imageFile: imageFile,
        prompt: prompt,
        model: model ?? defaultVisionModel,
        temperature: 0.1, // 降低温度以获得更稳定的结果
        maxTokens: 100,
      );
      
      // 清理结果，只保留数字和小数点
      final cleanResult = result.replaceAll(RegExp(r'[^\d\.\-\s]'), '').trim();
      return cleanResult.isEmpty ? '-' : cleanResult;
    } catch (e) {
      throw Exception('OpenRouter数字识别失败: $e');
    }
  }

  /// 测试API连接
  static Future<bool> testConnection({String? chatModel, String? visionModel}) async {
    try {
      // 测试文本对话
      final chatResult = await chatCompletion(
        message: '请回复"连接成功"',
        model: chatModel,
        maxTokens: 50,
      );
      
      if (!chatResult.contains('连接成功') && !chatResult.toLowerCase().contains('success')) {
        return false;
      }
      
      return true;
    } catch (e) {
      print('OpenRouter连接测试失败: $e');
      return false;
    }
  }

  /// 获取文件MIME类型
  static String _getMimeType(String filePath) {
    final extension = filePath.toLowerCase().split('.').last;
    switch (extension) {
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      case 'gif':
        return 'image/gif';
      case 'webp':
        return 'image/webp';
      default:
        return 'image/jpeg';
    }
  }

  /// 获取可用模型列表
  static List<String> getAvailableVisionModels() {
    return [
      'qwen/qwen2.5-vl-72b-instruct:free',
      'qwen/qwen2-vl-7b-instruct:free',
      'google/gemini-flash-1.5-8b',
      'anthropic/claude-3.5-sonnet',
      'openai/gpt-4o-mini',
    ];
  }

  static List<String> getAvailableChatModels() {
    return [
      'deepseek/deepseek-chat-v3-0324:free',
      'qwen/qwen-2.5-72b-instruct:free',
      'meta-llama/llama-3.1-8b-instruct:free',
      'microsoft/wizardlm-2-8x22b:free',
      'google/gemma-2-9b-it:free',
    ];
  }
}
