import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:http/http.dart' as http;
import 'package:get_storage/get_storage.dart';

/// Ollama视觉识别服务
/// 使用qwen2.5vl:7b模型进行图片识别和数字提取
class OllamaVisionService {
  static OllamaVisionService? _instance;
  static OllamaVisionService get instance => _instance ??= OllamaVisionService._();
  
  OllamaVisionService._();

  final GetStorage _storage = GetStorage();
  
  // 默认配置
  static const String _defaultServerUrl = 'https://cup-ollama.ailer.ltd';
  static const String _defaultApiKey = '123456';
  static const String _defaultModel = 'qwen2.5vl:7b';
  
  // 存储键
  static const String _serverUrlKey = 'ollama_server_url';
  static const String _apiKeyKey = 'ollama_api_key';
  static const String _modelKey = 'ollama_model';

  /// 获取服务端地址
  String get serverUrl => _storage.read(_serverUrlKey) ?? _defaultServerUrl;
  
  /// 获取API密钥
  String get apiKey => _storage.read(_apiKeyKey) ?? _defaultApiKey;
  
  /// 获取模型名称
  String get model => _storage.read(_modelKey) ?? _defaultModel;

  /// 设置服务端地址
  void setServerUrl(String url) {
    _storage.write(_serverUrlKey, url);
  }

  /// 设置API密钥
  void setApiKey(String key) {
    _storage.write(_apiKeyKey, key);
  }

  /// 设置模型名称
  void setModel(String modelName) {
    _storage.write(_modelKey, modelName);
  }

  /// 测试服务连接
  Future<bool> testConnection() async {
    try {
      final response = await http.get(
        Uri.parse('$serverUrl/api/tags'),
        headers: {
          'Authorization': 'Bearer $apiKey',
          'Content-Type': 'application/json',
        },
      ).timeout(const Duration(seconds: 10));

      return response.statusCode == 200;
    } catch (e) {
      print('连接测试失败: $e');
      return false;
    }
  }

  /// 识别图片中的数字
  Future<String> recognizeNumbers(File imageFile) async {
    try {
      // 读取图片文件并转换为base64
      final bytes = await imageFile.readAsBytes();
      final base64Image = base64Encode(bytes);

      // 构建请求体
      final requestBody = {
        'model': model,
        'prompt': '''这是一张仪表显示屏的图片，请完整提取其中显示的数值,注意小数点部分要保留。
只返回识别到的数字，不要包含其他文字说明。
如果有多个数字，请用空格分隔。
如果无法识别出数字，请返回"-"。''',
        'images': [base64Image],
        'stream': false,
        'options': {
          'temperature': 0.1,
          'top_p': 0.9,
        }
      };

      // 发送请求
      final response = await http.post(
        Uri.parse('$serverUrl/api/generate'),
        headers: {
          'Authorization': 'Bearer $apiKey',
          'Content-Type': 'application/json',
        },
        body: jsonEncode(requestBody),
      ).timeout(const Duration(seconds: 30));

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        final result = responseData['response'] ?? '';
        
        // 提取数字
        return _extractNumbers(result);
      } else {
        print('API请求失败: ${response.statusCode} - ${response.body}');
        return '-';
      }
    } catch (e) {
      print('图片识别失败: $e');
      return '-';
    }
  }

  /// 从响应文本中提取数字
  String _extractNumbers(String text) {
    // 移除所有非数字和小数点的字符，保留空格作为分隔符
    final cleanText = text.replaceAll(RegExp(r'[^\d\.\s]'), '');
    
    // 使用正则表达式匹配数字（包括小数）
    final numberPattern = RegExp(r'\d+\.?\d*');
    final matches = numberPattern.allMatches(cleanText);
    
    if (matches.isNotEmpty) {
      // 取第一个匹配的数字
      return matches.first.group(0) ?? '-';
    }
    
    return '-';
  }

  /// 批量识别多张图片
  Future<List<String>> recognizeMultipleImages(List<File> imageFiles) async {
    final results = <String>[];
    
    for (final imageFile in imageFiles) {
      final result = await recognizeNumbers(imageFile);
      results.add(result);
    }
    
    return results;
  }

  /// 获取服务状态信息
  Future<Map<String, dynamic>> getServiceStatus() async {
    try {
      final isConnected = await testConnection();
      return {
        'connected': isConnected,
        'serverUrl': serverUrl,
        'model': model,
        'lastCheck': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      return {
        'connected': false,
        'serverUrl': serverUrl,
        'model': model,
        'error': e.toString(),
        'lastCheck': DateTime.now().toIso8601String(),
      };
    }
  }

  /// 重置为默认配置
  void resetToDefaults() {
    _storage.remove(_serverUrlKey);
    _storage.remove(_apiKeyKey);
    _storage.remove(_modelKey);
  }
}

/// 识别结果模型
class VisionRecognitionResult {
  final String originalText;
  final String extractedNumber;
  final double confidence;
  final DateTime timestamp;

  VisionRecognitionResult({
    required this.originalText,
    required this.extractedNumber,
    required this.confidence,
    required this.timestamp,
  });

  Map<String, dynamic> toJson() {
    return {
      'originalText': originalText,
      'extractedNumber': extractedNumber,
      'confidence': confidence,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  factory VisionRecognitionResult.fromJson(Map<String, dynamic> json) {
    return VisionRecognitionResult(
      originalText: json['originalText'] ?? '',
      extractedNumber: json['extractedNumber'] ?? '-',
      confidence: (json['confidence'] ?? 0.0).toDouble(),
      timestamp: DateTime.parse(json['timestamp']),
    );
  }
}
