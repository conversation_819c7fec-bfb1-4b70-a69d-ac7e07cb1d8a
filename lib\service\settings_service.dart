import 'package:get_storage/get_storage.dart';

// OCR引擎类型
enum OCREngine {
  openrouter('OpenRouter'),
  ollama('Ollama'),
  local('本地模型');

  const OCREngine(this.displayName);
  final String displayName;
}

/// 设置服务 - 管理应用的所有设置项
class SettingsService {
  static final SettingsService _instance = SettingsService._internal();
  factory SettingsService() => _instance;
  SettingsService._internal();

  static SettingsService get instance => _instance;

  final GetStorage _storage = GetStorage();

  // 设置键名
  static const String _ocrEngineKey = 'ocr_engine';
  static const String _openrouterVisionModelKey = 'openrouter_vision_model';
  static const String _openrouterChatModelKey = 'openrouter_chat_model';
  static const String _kioskModeKey = 'kiosk_mode_enabled';
  static const String _ollamaServerUrlKey = 'ollama_server_url';
  static const String _ollamaApiKeyKey = 'ollama_api_key';
  static const String _ollamaModelKey = 'ollama_model';



  // OCR引擎设置
  OCREngine get ocrEngine {
    final value = _storage.read(_ocrEngineKey) ?? 'openrouter';
    return OCREngine.values.firstWhere(
      (e) => e.name == value,
      orElse: () => OCREngine.openrouter,
    );
  }

  set ocrEngine(OCREngine engine) {
    _storage.write(_ocrEngineKey, engine.name);
  }

  // OpenRouter模型设置
  String get openrouterVisionModel {
    return _storage.read(_openrouterVisionModelKey) ?? 'qwen/qwen2.5-vl-72b-instruct:free';
  }

  set openrouterVisionModel(String model) {
    _storage.write(_openrouterVisionModelKey, model);
  }

  String get openrouterChatModel {
    return _storage.read(_openrouterChatModelKey) ?? 'deepseek/deepseek-chat-v3-0324:free';
  }

  set openrouterChatModel(String model) {
    _storage.write(_openrouterChatModelKey, model);
  }

  // Kiosk模式设置
  bool get kioskModeEnabled {
    return _storage.read(_kioskModeKey) ?? true;
  }

  set kioskModeEnabled(bool enabled) {
    _storage.write(_kioskModeKey, enabled);
  }

  // Ollama设置
  String get ollamaServerUrl {
    return _storage.read(_ollamaServerUrlKey) ?? 'https://cup-ollama.ailer.ltd';
  }

  set ollamaServerUrl(String url) {
    _storage.write(_ollamaServerUrlKey, url);
  }

  String get ollamaApiKey {
    return _storage.read(_ollamaApiKeyKey) ?? '';
  }

  set ollamaApiKey(String key) {
    _storage.write(_ollamaApiKeyKey, key);
  }

  String get ollamaModel {
    return _storage.read(_ollamaModelKey) ?? 'qwen2.5vl:7b';
  }

  set ollamaModel(String model) {
    _storage.write(_ollamaModelKey, model);
  }

  // 重置所有设置
  void resetAllSettings() {
    _storage.erase();
  }

  // 导出设置
  Map<String, dynamic> exportSettings() {
    return {
      'ocr_engine': ocrEngine.name,
      'openrouter_vision_model': openrouterVisionModel,
      'openrouter_chat_model': openrouterChatModel,
      'kiosk_mode_enabled': kioskModeEnabled,
      'ollama_server_url': ollamaServerUrl,
      'ollama_api_key': ollamaApiKey,
      'ollama_model': ollamaModel,
    };
  }

  // 导入设置
  void importSettings(Map<String, dynamic> settings) {
    settings.forEach((key, value) {
      _storage.write(key, value);
    });
  }
}
