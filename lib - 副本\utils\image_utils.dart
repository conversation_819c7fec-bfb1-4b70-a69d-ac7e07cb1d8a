import 'dart:io';
import 'dart:typed_data';
import 'package:image/image.dart' as img;
import 'package:image_picker/image_picker.dart';

class ImageUtils {
  /// 精确定位仪表框选区域
  static Future<File?> cropMeterDigits(File pickedFile, int x, int y, int w, int h) async {
    try {
      final bytes = await pickedFile.readAsBytes();
      img.Image? originalImage = img.decodeImage(bytes);
      if (originalImage == null) {
        print('无法解码原始图片');
        return null;
      }

      print('原始图片尺寸: ${originalImage.width} x ${originalImage.height}');
      print('请求裁剪区域: x=$x, y=$y, w=$w, h=$h');

      // 边界安全检查 - 改进逻辑
      if (x >= originalImage.width || y >= originalImage.height) {
        print('裁剪起始点超出图片边界');
        return null;
      }

      final safeX = x.clamp(0, originalImage.width - 1);
      final safeY = y.clamp(0, originalImage.height - 1);
      final maxW = originalImage.width - safeX;
      final maxH = originalImage.height - safeY;
      final safeW = w.clamp(1, maxW);
      final safeH = h.clamp(1, maxH);

      print('实际裁剪区域: x=$safeX, y=$safeY, w=$safeW, h=$safeH');

      // 确保裁剪区域有意义的大小
      if (safeW < 10 || safeH < 10) {
        print('裁剪区域太小: ${safeW}x${safeH}');
        return null;
      }

      img.Image croppedImage = img.copyCrop(
        originalImage,
        x: safeX,
        y: safeY,
        width: safeW,
        height: safeH,
      );

      print('裁剪完成，结果尺寸: ${croppedImage.width} x ${croppedImage.height}');

      final tempDir = Directory.systemTemp;
      final outputFile = File(
        '${tempDir.path}/processed_${DateTime.now().millisecondsSinceEpoch}.png',
      );
      await outputFile.writeAsBytes(img.encodePng(croppedImage));

      return outputFile;
    } catch (e) {
      print('图像裁剪处理失败: $e');
      return null;
    }
  }

  /// 图像预处理：针对仪表屏幕优化识别效果
  static Future<File> preprocess(File imageFile) async {
    try {
      final bytes = await imageFile.readAsBytes();
      img.Image? original = img.decodeImage(bytes);
      if (original == null) return imageFile;

      final gray = img.grayscale(original);
      final binary = img.Image(width: gray.width, height: gray.height);
      
      for (int y = 0; y < gray.height; y++) {
        for (int x = 0; x < gray.width; x++) {
          final pixel = gray.getPixel(x, y);
          final luminance = img.getLuminance(pixel);
          binary.setPixel(x, y, luminance > 90 
            ? img.ColorRgb8(255, 255, 255)
            : img.ColorRgb8(0, 0, 0));
        }
      }
      
      final tempDir = Directory.systemTemp;
      final outputFile = File(
        '${tempDir.path}/processed_${DateTime.now().millisecondsSinceEpoch}.png',
      );
      await outputFile.writeAsBytes(img.encodePng(binary));
      
      return outputFile;
    } catch (e) {
      print('图像预处理失败: $e');
      return imageFile;
    }
  }

  /// 第二种OCR预处理方法
  static Future<File> preprocessForOCR2(File imageFile) async {
    try {
      final bytes = await imageFile.readAsBytes();
      img.Image? original = img.decodeImage(bytes);
      if (original == null) return imageFile;

      // 1. 调整大小
      img.Image resized = img.copyResize(original, width: 900);
      
      // 2. 转换为灰度图
      img.Image gray = img.grayscale(resized);
      
      // 3. 二值化处理
      final binary = img.Image(width: gray.width, height: gray.height);
      for (int y = 0; y < gray.height; y++) {
        for (int x = 0; x < gray.width; x++) {
          final pixel = gray.getPixel(x, y);
          final luminance = img.getLuminance(pixel);
          binary.setPixel(x, y, luminance > 128 
            ? img.ColorRgb8(255, 255, 255)
            : img.ColorRgb8(0, 0, 0));
        }
      }
      
      // 4. 取反操作
      final inverted = img.invert(binary);
      
      final tempDir = Directory.systemTemp;
      final outputFile = File(
        '${tempDir.path}/preprocessed2_${DateTime.now().millisecondsSinceEpoch}.png',
      );
      await outputFile.writeAsBytes(img.encodePng(inverted));
      
      return outputFile;
    } catch (e) {
      print('图像预处理2失败: $e');
      return imageFile;
    }
  }
}
