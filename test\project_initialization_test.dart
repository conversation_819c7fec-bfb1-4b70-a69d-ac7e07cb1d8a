import 'package:flutter_test/flutter_test.dart';
import 'package:a325_lya_1000/service/project_initialization_service.dart';

void main() {
  group('ProjectInitializationService Tests', () {
    late ProjectInitializationService service;

    setUp(() {
      service = ProjectInitializationService.instance;
    });

    test('ProjectInitializationService should be a singleton', () {
      final instance1 = ProjectInitializationService.instance;
      final instance2 = ProjectInitializationService.instance;
      expect(instance1, same(instance2));
    });

    test('should have correct initialization methods', () {
      expect(service.checkAndInitializeDefaultProject, isA<Function>());
      expect(service.resetInitializationStatus, isA<Function>());
      expect(service.hasCreatedDefaultProject, isA<bool>());
      expect(service.getCurrentProjectInfo, isA<Function>());
    });

    test('should return current project info', () {
      final projectInfo = service.getCurrentProjectInfo();
      expect(projectInfo, isA<Map<String, String?>>());
      expect(projectInfo.containsKey('name'), true);
      expect(projectInfo.containsKey('uniqueId'), true);
    });

    test('should have reset initialization status method', () {
      // 验证重置方法存在
      expect(service.resetInitializationStatus, isA<Function>());
    });

    test('should handle initialization gracefully', () {
      // 这个测试验证初始化方法存在且可调用
      expect(service.checkAndInitializeDefaultProject, isA<Function>());
    });
  });

  group('Device Status Manager Power Saving Tests', () {
    test('should not auto-start monitoring to save power', () {
      // 这个测试验证设备状态管理器不会自动启动监控
      // 实际的验证需要在集成测试中进行
      expect(true, true); // 占位测试
    });
  });
}
