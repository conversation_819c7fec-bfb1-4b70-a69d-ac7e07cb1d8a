import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'pages/data_processing_page.dart';
import 'pages/usage_guide_page.dart';
import 'pages/project_management_page.dart';
import 'pages/dataCollectorpage.dart';
import 'package:get_storage/get_storage.dart';
import 'theme/industrial_theme.dart';
import 'widgets/device_status_widget.dart';
import 'utils/device_status_manager.dart';
import 'pages/device_detail_page.dart';
import 'pages/system_settings_page.dart';
import 'utils/kiosk_manager.dart';
import 'utils/power_manager.dart';
import 'utils/battery_manager.dart';
import 'widgets/battery_status_widget.dart';
import 'service/project_initialization_service.dart';

void main() async {
  // 确保Flutter绑定已初始化
  WidgetsFlutterBinding.ensureInitialized();

  // 初始化存储
  await GetStorage.init();

  // 设置首选的方向为横向
  SystemChrome.setPreferredOrientations([
    DeviceOrientation.landscapeLeft,
    DeviceOrientation.landscapeRight,
  ]);

  // 初始化kiosk模式
  await KioskManager.instance.initialize();

  // 初始化电池管理器
  await BatteryManager.instance.initialize();

  // 检查并初始化默认项目
  await ProjectInitializationService.instance.checkAndInitializeDefaultProject();

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: '洛伊奥-LYA-1000 数据采集仪',
      debugShowCheckedModeBanner: false, // 移除debug标记
      theme: IndustrialTheme.getIndustrialTheme(),
      home: const MyHomePage(title: '洛伊奥智能数据采集仪 LYA-1000'),
    );
  }
}

class MyHomePage extends StatefulWidget {
  const MyHomePage({super.key, required this.title});

  final String title;

  @override
  State<MyHomePage> createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  late DeviceStatusManager _deviceStatusManager;

  final GetStorage _storage = GetStorage();
  final KioskManager _kioskManager = KioskManager.instance;

  @override
  void initState() {
    super.initState();

    // 初始化设备状态管理器
    _deviceStatusManager = DeviceStatusManager.instance;
    _deviceStatusManager.addListener(_onDeviceStatusChanged);

    // 确保停止任何正在运行的监控以节省功耗
    if (_deviceStatusManager.isMonitoring) {
      _deviceStatusManager.stopMonitoring();
    }

    // 停用自动启动设备状态监控以节省功耗
    // 用户可以通过设备状态页面手动启动监控
    // if (!_deviceStatusManager.isMonitoring) {
    //   _deviceStatusManager.startMonitoring();
    // }

    // 添加渐变动画效果
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _animation = CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOutCubic,
    );

    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    _deviceStatusManager.removeListener(_onDeviceStatusChanged);
    super.dispose();
  }

  /// 设备状态变化回调
  void _onDeviceStatusChanged() {
    if (mounted) {
      setState(() {});
    }
  }

  /// 处理管理员退出手势
  void _handleAdminExitGesture() async {
    if (_kioskManager.isAdminExitEnabled && _kioskManager.handleAdminExitGesture()) {
      if (!mounted) return;

      // 显示管理员密码验证对话框
      final passwordCorrect = await _kioskManager.showAdminPasswordDialog(context);
      if (passwordCorrect && mounted) {
        // 密码正确，显示最终确认对话框
        final shouldExit = await _kioskManager.showAdminExitDialog(context);
        if (shouldExit) {
          await _kioskManager.disableKioskMode();
          await _kioskManager.stopLockTask();
          _kioskManager.disableAdminExit();

          // 显示退出成功提示
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('已退出kiosk模式'),
                backgroundColor: Colors.green,
              ),
            );
          }
        } else {
          _kioskManager.resetAdminExitGesture();
        }
      } else {
        _kioskManager.resetAdminExitGesture();
      }
    }
  }

  /// 处理电源操作
  void _handlePowerAction(String action) {
    switch (action) {
      case 'reboot':
        PowerManager.instance.rebootDevice(context);
        break;
      case 'shutdown':
        PowerManager.instance.shutdownDevice(context);
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return GestureDetector(
      onTap: _handleAdminExitGesture,
      child: Scaffold(
      appBar: AppBar(
        backgroundColor: colorScheme.primary,
        toolbarHeight: 48, // 减小AppBar高度
        title: Row(
          children: [
            Image.asset(
              'assets/logo.png', // 请确保添加公司logo资源
              height: 32, // 减小logo大小
              width: 32,
              errorBuilder: (context, error, stackTrace) => const Icon(Icons.analytics, size: 24, color: Colors.white),
            ),
            const SizedBox(width: 8),
            Text(
              widget.title,
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 16, // 减小标题文字大小
              ),
            ),
          ],
        ),
        actions: [
          // 右上角的额外信息
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12.0),
            child: Row(
              children: [
                const Icon(Icons.access_time, color: Colors.white70, size: 14),
                const SizedBox(width: 4),
                StreamBuilder(
                    stream: Stream.periodic(const Duration(seconds: 1)),
                    builder: (context, snapshot) {
                      return Text(
                        _getFormattedDateTime(),
                        style: const TextStyle(color: Colors.white70, fontSize: 12),
                      );
                    }
                ),
                const SizedBox(width: 12),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.account_circle, color: Colors.white, size: 14),
                      const SizedBox(width: 4),
                      const Text(
                        "管理员",
                        style: TextStyle(color: Colors.white, fontSize: 12),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 12),
                // 系统设置图标
                IconButton(
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const SystemSettingsPage(),
                      ),
                    );
                  },
                  icon: const Icon(
                    Icons.settings,
                    color: Colors.white,
                    size: 20,
                  ),
                  tooltip: '系统设置',
                ),
                const SizedBox(width: 8),
                // 电池状态显示
                const BatteryStatusWidget(),
                const SizedBox(width: 8),
                // 电源管理下拉菜单
                PopupMenuButton<String>(
                  onSelected: (String value) {
                    _handlePowerAction(value);
                  },
                  icon: const Icon(
                    Icons.power_settings_new,
                    color: Colors.white,
                    size: 20,
                  ),
                  tooltip: '电源管理',
                  color: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  itemBuilder: (BuildContext context) => [
                    const PopupMenuItem<String>(
                      value: 'reboot',
                      child: Row(
                        children: [
                          Icon(Icons.refresh, color: Color(0xFF2C3E50), size: 18),
                          SizedBox(width: 12),
                          Text(
                            '重启设备',
                            style: TextStyle(
                              color: Color(0xFF2C3E50),
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const PopupMenuItem<String>(
                      value: 'shutdown',
                      child: Row(
                        children: [
                          Icon(Icons.power_off, color: Color(0xFFE74C3C), size: 18),
                          SizedBox(width: 12),
                          Text(
                            '关机',
                            style: TextStyle(
                              color: Color(0xFFE74C3C),
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
      body: FadeTransition(
        opacity: _animation,
        child: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                colorScheme.surfaceContainerLowest,
                colorScheme.surfaceContainerHighest,
              ],
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.fromLTRB(16, 12, 16, 12), // 减小内边距
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 欢迎信息
                Text(
                  "欢迎使用洛伊奥智能数据采集系统",
                  style: TextStyle(
                    fontSize: 20, // 减小标题大小
                    fontWeight: FontWeight.bold,
                    color: colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  "请选择以下功能开始您的操作",
                  style: TextStyle(
                    fontSize: 14, // 减小字体大小
                    color: colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 16), // 减小间距

                // 主要功能卡片 - 一行四个卡片
                // 主要功能卡片 - 一行四个卡片
                Expanded(
                  child: Row(
                    children: [
                      Expanded(
                        child: _buildFeatureCard(
                          context,
                          title: "项目管理",
                          description: "创建和管理管道检测项目",
                          icon: Icons.folder_special_rounded,
                          iconColor: const Color(0xFF4285F4),
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(builder: (context) => ProjectManagementPage()),
                            );
                          },
                        ),
                      ),
                      const SizedBox(width: 12), // 卡片之间的间距
                      Expanded(
                        child: _buildFeatureCard(
                          context,
                          title: "数据采集",
                          description: "对设备进行实时数据采集",
                          icon: Icons.sensors,
                          iconColor: const Color(0xFF0F9D58),
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => DataCollectionPage(projectName: '智能数据采集',projectUniqueId: _storage.read('currentProjectUniqueId')),
                              ),
                            );
                          },
                        ),
                      ),
                      const SizedBox(width: 12), // 卡片之间的间距
                      Expanded(
                        child: _buildFeatureCard(
                          context,
                          title: "数据处理",
                          description: "对项目中采集的数据进行管理",
                          icon: Icons.insert_chart_outlined,
                          iconColor: const Color(0xFFDB4437),
                          onTap: () {
                            // 数据处理页面的导航
                            // _showComingSoonDialog(context);
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => DataProcessingPage(
                                  projectUniqueId: _storage.read('currentProjectUniqueId'),
                                  projectName: _storage.read('currentProjectName'),
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                      const SizedBox(width: 12), // 卡片之间的间距
                      Expanded(
                        child: _buildFeatureCard(
                          context,
                          title: "使用说明",
                          description: "查看采集仪的使用手册和支持",
                          icon: Icons.help_outline_rounded,
                          iconColor: const Color(0xFFF4B400),
                          onTap: () {
                            // 使用说明页面的导航
                            // _showComingSoonDialog(context);
                            Navigator.push(
                              context,
                              MaterialPageRoute(builder: (context) => UsageGuidePage()),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),

                // 底部状态信息
                const SizedBox(height: 12),
                Container(
                  decoration: BoxDecoration(
                    color: colorScheme.surfaceContainerHighest.withValues(alpha: 0.5),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: InkWell(
                    onTap: () {
                      // 导航到设备详细状态页面
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const DeviceDetailPage(),
                        ),
                      );
                    },
                    borderRadius: BorderRadius.circular(8),
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                      child: Row(
                        children: [
                          // Icon(
                          //   Icons.info_outline,
                          //   size: 14,
                          //   color: colorScheme.primary,
                          // ),
                          // const SizedBox(width: 6),
                          // Text(
                          //   "系统状态:",
                          //   style: TextStyle(
                          //     fontSize: 12,
                          //     color: colorScheme.onSurface,
                          //   ),
                          // ),
                          // const SizedBox(width: 8),
                          // 设备状态组件（紧凑模式）
                          Expanded(
                            child: DeviceStatusWidget(
                              showDetails: false,
                              onRefresh: () {
                                DeviceStatusManager.instance.checkAllDevices();
                              },
                            ),
                          ),
                          const SizedBox(width: 16),
                          Text(
                            "软件版本: v1.0.0",
                            style: TextStyle(
                              fontSize: 12,
                              color: colorScheme.onSurface,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Icon(
                            Icons.arrow_forward_ios,
                            size: 12,
                            color: colorScheme.primary,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    ),
    );
  }

  // 创建功能卡片 - 优化版本适合小屏幕
  Widget _buildFeatureCard(
      BuildContext context, {
        required String title,
        required String description,
        required IconData icon,
        required Color iconColor,
        required VoidCallback onTap,
      }) {
    final colorScheme = Theme.of(context).colorScheme;

    return Card(
      clipBehavior: Clip.antiAlias,
      elevation: 3, // 减小阴影
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12), // 减小边框圆角
      ),
      child: InkWell(
        onTap: onTap,
        child: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.white,
                colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
              ],
            ),
          ),
          child: Stack(
            children: [
              // 背景装饰
              Positioned(
                right: 65,
                bottom: 65,
                child: Icon(
                  icon,
                  size: 80, // 减小背景图标大小
                  color: iconColor.withValues(alpha: 0.08),
                ),
              ),
              // 内容
              Padding(
                padding: const EdgeInsets.all(12.0), // 减小内边距
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 图标和标题行
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8), // 减小图标容器内边距
                          decoration: BoxDecoration(
                            color: iconColor.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(10), // 减小边框圆角
                          ),
                          child: Icon(
                            icon,
                            color: iconColor,
                            size: 22, // 减小图标大小
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            title,
                            style: const TextStyle(
                              fontSize: 16, // 减小标题文字大小
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    // 描述
                    Text(
                      description,
                      style: TextStyle(
                        fontSize: 12, // 减小描述文字大小
                        color: colorScheme.onSurface,
                        height: 1.3,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const Spacer(),
                    // 底部按钮
                    Align(
                      alignment: Alignment.bottomRight,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4), // 减小按钮内边距
                        decoration: BoxDecoration(
                          color: iconColor.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12), // 减小边框圆角
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              "进入",
                              style: TextStyle(
                                color: iconColor,
                                fontWeight: FontWeight.w600,
                                fontSize: 12, // 减小按钮文字大小
                              ),
                            ),
                            const SizedBox(width: 2),
                            Icon(
                              Icons.arrow_forward_rounded,
                              color: iconColor,
                              size: 12, // 减小按钮图标大小
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 获取格式化的当前日期时间
  String _getFormattedDateTime() {
    final now = DateTime.now();
    final hours = now.hour.toString().padLeft(2, '0');
    final minutes = now.minute.toString().padLeft(2, '0');
    final year = now.year.toString();
    final month = now.month.toString().padLeft(2, '0');
    final day = now.day.toString().padLeft(2, '0');

    return "$year-$month-$day $hours:$minutes"; // 移除秒数以节省空间
  }


}
