# Kiosk模式测试指南

## 测试环境准备

### 1. 编译应用
```bash
flutter clean
flutter pub get
flutter build apk --release
```

### 2. 安装到设备
```bash
flutter install
```

## 功能测试清单

### ✅ 基础Kiosk模式测试

#### 1. 应用启动测试
- [ ] 应用启动后自动进入全屏模式
- [ ] 状态栏和导航栏被隐藏
- [ ] 应用界面正常显示
- [ ] 横屏模式正确设置

#### 2. 退出防护测试
- [ ] 按返回键无法退出应用
- [ ] 按Home键无法退出应用
- [ ] 按菜单键无法退出应用
- [ ] 按任务切换键无法退出应用

#### 3. 系统UI隐藏测试
- [ ] 状态栏完全隐藏
- [ ] 导航栏完全隐藏
- [ ] 下拉状态栏被禁用
- [ ] 上滑导航栏被禁用

### ✅ 管理员退出机制测试

#### 1. 手势退出测试
- [ ] 连续点击屏幕7次触发密码输入
- [ ] 输入错误密码显示错误提示
- [ ] 输入正确密码（LYA1000ADMIN）进入确认对话框
- [ ] 确认退出后成功退出kiosk模式
- [ ] 取消退出后继续保持kiosk模式

#### 2. 管理员设置页面测试
- [ ] 可以访问管理员设置页面
- [ ] 可以查看kiosk模式状态
- [ ] 可以查看设备管理员状态
- [ ] 可以手动切换kiosk模式
- [ ] 可以手动切换设备管理员

### ✅ 设备管理员功能测试

#### 1. 设备管理员启用测试
- [ ] 首次启动时提示启用设备管理员
- [ ] 成功启用设备管理员权限
- [ ] 设备管理员状态正确显示

#### 2. 锁定任务模式测试
- [ ] 应用成功启动锁定任务模式
- [ ] 锁定任务模式下无法切换应用
- [ ] 锁定任务模式下无法访问系统设置

### ✅ 开机自启动测试

#### 1. 开机启动测试
- [ ] 设备重启后应用自动启动
- [ ] 启动后自动进入kiosk模式
- [ ] 后台服务正常运行
- [ ] 通知栏显示服务运行状态

#### 2. 服务保活测试
- [ ] 应用被意外关闭后自动重启
- [ ] 后台服务被杀死后自动重启
- [ ] 系统内存不足时应用优先级保持

### ✅ 应用功能完整性测试

#### 1. 核心功能测试
- [ ] 项目管理功能正常
- [ ] 数据采集功能正常
- [ ] 数据处理功能正常
- [ ] 使用指南功能正常

#### 2. 设备状态监控测试
- [ ] GPS设备状态正常显示
- [ ] 网络连接状态正常显示
- [ ] 摄像头状态正常显示
- [ ] 麦克风状态正常显示

## 测试步骤详解

### 步骤1：基础功能验证
1. 安装应用到测试设备
2. 启动应用，观察是否自动进入全屏模式
3. 尝试各种退出操作，确认无法退出
4. 检查系统UI是否完全隐藏

### 步骤2：管理员退出验证
1. 在主界面连续快速点击7次
2. 输入错误密码，确认显示错误提示
3. 输入正确密码"LYA1000ADMIN"
4. 在确认对话框中选择"确认退出"
5. 验证是否成功退出kiosk模式

### 步骤3：设备管理员验证
1. 在设置中查看设备管理员状态
2. 如未启用，按提示启用设备管理员
3. 验证锁定任务模式是否生效
4. 尝试访问系统设置，确认被阻止

### 步骤4：开机自启动验证
1. 重启测试设备
2. 观察应用是否自动启动
3. 检查通知栏是否显示服务运行
4. 验证kiosk模式是否自动启用

### 步骤5：压力测试
1. 长时间运行应用（24小时）
2. 模拟各种异常情况（强制关闭、内存不足等）
3. 验证应用的稳定性和恢复能力

## 常见问题排查

### 问题1：应用无法进入kiosk模式
**可能原因：**
- 设备管理员权限未启用
- 系统版本不兼容
- 权限配置错误

**解决方案：**
1. 检查设备管理员是否启用
2. 确认Android版本兼容性
3. 重新安装应用

### 问题2：管理员退出不工作
**可能原因：**
- 手势识别失败
- 密码输入错误
- 对话框显示异常

**解决方案：**
1. 确保连续快速点击7次
2. 检查密码是否正确（LYA1000ADMIN）
3. 重启应用重试

### 问题3：开机自启动失败
**可能原因：**
- 权限被系统限制
- 服务被优化软件禁用
- 广播接收器未注册

**解决方案：**
1. 在系统设置中允许自启动
2. 将应用加入白名单
3. 检查权限配置

## 测试报告模板

### 测试环境
- 设备型号：
- Android版本：
- 应用版本：
- 测试日期：

### 测试结果
- 基础kiosk模式：✅/❌
- 管理员退出机制：✅/❌
- 设备管理员功能：✅/❌
- 开机自启动：✅/❌
- 应用功能完整性：✅/❌

### 发现的问题
1. 问题描述
2. 复现步骤
3. 预期结果
4. 实际结果

### 建议改进
1. 改进建议1
2. 改进建议2
3. 改进建议3

## 部署建议

### 生产环境部署前检查
- [ ] 所有测试用例通过
- [ ] 性能测试满足要求
- [ ] 安全测试通过
- [ ] 用户接受度测试完成

### 部署步骤
1. 备份现有系统
2. 安装新版本应用
3. 配置设备管理员权限
4. 设置开机自启动
5. 验证kiosk模式功能
6. 培训操作人员

### 维护建议
- 定期检查应用运行状态
- 监控设备性能指标
- 及时更新应用版本
- 备份重要数据
