import 'dart:async';
import 'package:flutter/material.dart';
import '../service/gps_service.dart';
import '../service/serial_gps_service.dart';
import '../widgets/industrial_widgets.dart';
import '../theme/industrial_theme.dart';

/// 串口GPS使用示例页面
class SerialGPSUsageExample extends StatefulWidget {
  const SerialGPSUsageExample({Key? key}) : super(key: key);

  @override
  State<SerialGPSUsageExample> createState() => _SerialGPSUsageExampleState();
}

class _SerialGPSUsageExampleState extends State<SerialGPSUsageExample> {
  final GPSService _gpsService = GPSService.instance;
  bool _isSerialGPSConnected = false;
  GPSData? _latestGPSData;
  String _connectionStatus = '未连接';

  @override
  void initState() {
    super.initState();
    _checkSerialGPSStatus();
  }

  /// 检查串口GPS状态
  void _checkSerialGPSStatus() {
    setState(() {
      _isSerialGPSConnected = _gpsService.isSerialGPSConnected();
      _latestGPSData = _gpsService.getSerialGPSData();
      _connectionStatus = _isSerialGPSConnected ? '已连接' : '未连接';
    });
  }

  /// 连接串口GPS
  Future<void> _connectSerialGPS() async {
    setState(() {
      _connectionStatus = '连接中...';
    });

    try {
      final success = await _gpsService.enableSerialGPS();
      setState(() {
        _isSerialGPSConnected = success;
        _connectionStatus = success ? '已连接' : '连接失败';
      });

      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('串口GPS连接成功！'),
            backgroundColor: IndustrialTheme.successGreen,
          ),
        );
        
        // 开始监听GPS数据更新
        _startGPSDataMonitoring();
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('串口GPS连接失败，请检查设备连接'),
            backgroundColor: IndustrialTheme.errorRed,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _connectionStatus = '连接错误';
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('连接错误: $e'),
          backgroundColor: IndustrialTheme.errorRed,
        ),
      );
    }
  }

  /// 断开串口GPS
  Future<void> _disconnectSerialGPS() async {
    await _gpsService.disableSerialGPS();
    setState(() {
      _isSerialGPSConnected = false;
      _latestGPSData = null;
      _connectionStatus = '已断开';
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('串口GPS已断开'),
        backgroundColor: IndustrialTheme.warningAmber,
      ),
    );
  }

  /// 开始GPS数据监听
  void _startGPSDataMonitoring() {
    // 每秒更新一次GPS数据显示
    Timer.periodic(const Duration(seconds: 1), (timer) {
      if (!_isSerialGPSConnected) {
        timer.cancel();
        return;
      }

      final newData = _gpsService.getSerialGPSData();
      if (newData != null && mounted) {
        setState(() {
          _latestGPSData = newData;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('串口GPS示例'),
        backgroundColor: IndustrialTheme.primaryBlue,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 连接状态卡片
            IndustrialInfoPanel(
              title: '串口GPS连接状态',
              icon: Icons.usb,
              headerColor: IndustrialTheme.getModuleColor('gps'),
              children: [
                IndustrialDataRow(
                  label: '设备路径',
                  value: '/dev/ttyUSB3',
                  icon: Icons.device_hub,
                ),
                IndustrialDataRow(
                  label: '波特率',
                  value: '115200',
                  icon: Icons.speed,
                ),
                IndustrialDataRow(
                  label: '连接状态',
                  value: _connectionStatus,
                  icon: _isSerialGPSConnected ? Icons.check_circle : Icons.error,
                  valueColor: _isSerialGPSConnected 
                      ? IndustrialTheme.successGreen 
                      : IndustrialTheme.errorRed,
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // 控制按钮
            Row(
              children: [
                Expanded(
                  child: IndustrialActionButton(
                    label: '连接GPS',
                    icon: Icons.link,
                    backgroundColor: IndustrialTheme.successGreen,
                    onPressed: _isSerialGPSConnected ? null : _connectSerialGPS,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: IndustrialActionButton(
                    label: '断开GPS',
                    icon: Icons.link_off,
                    backgroundColor: IndustrialTheme.errorRed,
                    onPressed: _isSerialGPSConnected ? _disconnectSerialGPS : null,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // GPS数据显示
            if (_latestGPSData != null) ...[
              IndustrialInfoPanel(
                title: 'GPS数据',
                icon: Icons.gps_fixed,
                headerColor: IndustrialTheme.getModuleColor('gps'),
                children: [
                  IndustrialDataRow(
                    label: '纬度',
                    value: '${_latestGPSData!.latitude.toStringAsFixed(8)}°',
                    icon: Icons.my_location,
                    isHighlighted: true,
                  ),
                  IndustrialDataRow(
                    label: '经度',
                    value: '${_latestGPSData!.longitude.toStringAsFixed(8)}°',
                    icon: Icons.my_location,
                    isHighlighted: true,
                  ),
                  if (_latestGPSData!.altitude != null)
                    IndustrialDataRow(
                      label: '海拔高度',
                      value: '${_latestGPSData!.altitude!.toStringAsFixed(2)} m',
                      icon: Icons.height,
                    ),
                  if (_latestGPSData!.fixQuality != null)
                    IndustrialDataRow(
                      label: '定位质量',
                      value: _latestGPSData!.fixQuality!,
                      icon: Icons.signal_cellular_alt,
                      valueColor: _getQualityColor(_latestGPSData!.fixQuality!),
                    ),
                  if (_latestGPSData!.satelliteCount != null)
                    IndustrialDataRow(
                      label: '卫星数量',
                      value: '${_latestGPSData!.satelliteCount} 颗',
                      icon: Icons.satellite,
                    ),
                  IndustrialDataRow(
                    label: '更新时间',
                    value: _formatTime(_latestGPSData!.timestamp),
                    icon: Icons.access_time,
                  ),
                ],
              ),
              
              const SizedBox(height: 16),
              
              // 地图链接按钮
              IndustrialActionButton(
                label: '在地图中查看',
                icon: Icons.map,
                backgroundColor: IndustrialTheme.primaryBlue,
                width: double.infinity,
                onPressed: () {
                  final url = 'https://www.google.com/maps?q=${_latestGPSData!.latitude},${_latestGPSData!.longitude}';
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('地图链接: $url'),
                      duration: const Duration(seconds: 5),
                    ),
                  );
                },
              ),
            ] else if (_isSerialGPSConnected) ...[
              const Center(
                child: Column(
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text('等待GPS数据...'),
                  ],
                ),
              ),
            ] else ...[
              const Center(
                child: Text(
                  '请先连接串口GPS设备',
                  style: TextStyle(
                    fontSize: 16,
                    color: IndustrialTheme.textSecondary,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// 获取定位质量对应的颜色
  Color _getQualityColor(String quality) {
    switch (quality) {
      case 'RTK固定解':
      case 'RTK浮点解':
        return IndustrialTheme.successGreen;
      case 'GPS定位':
      case 'DGPS定位':
        return IndustrialTheme.primaryBlue;
      case '无效':
        return IndustrialTheme.errorRed;
      default:
        return IndustrialTheme.warningAmber;
    }
  }

  /// 格式化时间显示
  String _formatTime(DateTime time) {
    return '${time.hour.toString().padLeft(2, '0')}:'
           '${time.minute.toString().padLeft(2, '0')}:'
           '${time.second.toString().padLeft(2, '0')}';
  }
}
