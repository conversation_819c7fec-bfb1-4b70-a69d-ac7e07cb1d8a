# Excel导出功能说明

## 功能概述

项目管理页面右上角的"导出EXCEL"按钮已经开发完成，可以将当前选择的项目数据导出为Excel表格文件。

## 功能特点

1. **一键导出**: 点击"导出EXCEL"按钮即可导出当前选择的项目
2. **完整数据**: 包含项目基本信息和所有数据收集记录
3. **标准格式**: 按照用户要求的Excel表格格式进行导出
4. **自动保存**: 文件自动保存到应用文档目录下的Download文件夹

## 使用方法

1. 在项目管理页面左侧选择要导出的项目
2. 点击右上角的"导出EXCEL"按钮
3. 系统会自动生成Excel文件并保存
4. 导出完成后会显示文件保存路径

## Excel文件结构

### 项目基本信息部分
- A1: 检测地点 - B1: 项目位置
- A2: 检测日期 - B2: 项目日期  
- A3: 管径 - B3: 管径信息
- A4: 壁厚 - B4: 壁厚信息
- A5: 防腐层 - B5: 防腐层信息
- A6: 工作频率 - B6: 工作频率
- A7: 项目名称 - B7: 项目名称

### 数据收集表头（第9行）
- A9: 序号
- B9: 距离
- C9: 里程
- D9: 电流mA
- E9: 埋深
- F9: 描述
- G9: 北纬
- H9: 东经
- I9: 高度
- J9: 东经（备用）
- K9: 东经（备用）
- L9: 仪表图片

### 数据记录（从第10行开始）
每行包含一条数据收集记录，包括：
- 序号（自动编号）
- 距离、里程、电流、埋深等测量数据
- GPS坐标信息（纬度、经度、海拔）
- 描述信息
- **仪表图片**：直接嵌入到Excel单元格中的实际图片（不是路径）

## 文件保存位置

Excel文件保存在应用文档目录下的Download文件夹中：
- 路径格式: `/data/data/com.example.a325_lya_1000/app_flutter/documents/Download/`
- 文件名格式: `项目名称_时间戳.xlsx`

## 图片嵌入功能

### 图片处理特点
- **直接嵌入**: 图片以二进制数据直接嵌入Excel文件，不是路径引用
- **自动调整**: 图片大小自动调整为120x80像素，适合表格显示
- **行高适配**: 包含图片的行高自动调整为80像素以适应图片
- **错误处理**: 如果图片文件不存在或加载失败，显示相应提示信息

### 图片状态说明
- **有图片**: 显示实际的仪表图片
- **无图片**: 显示"无图片"文本
- **加载失败**: 显示"图片加载失败"文本

## 技术实现

### 使用的依赖包
- `syncfusion_flutter_xlsio`: Excel文件生成和图片嵌入
- `path_provider`: 获取文件保存路径

### 主要功能
1. **数据查询**: 从SQLite数据库中查询项目信息和数据收集记录
2. **Excel生成**: 使用Syncfusion XLSIO库创建Excel工作簿
3. **格式设置**: 按照指定格式设置表头和数据
4. **图片嵌入**: 读取图片文件并直接嵌入到Excel单元格中
5. **文件保存**: 将生成的Excel文件保存到指定目录

## 注意事项

1. **项目选择**: 必须先在左侧列表中选择一个项目才能导出
2. **数据完整性**: 导出的数据基于当前数据库中的实际记录
3. **文件权限**: 应用需要有文件写入权限
4. **存储空间**: 确保设备有足够的存储空间

## 错误处理

- 如果未选择项目，会提示"请先选择一个项目"
- 如果导出过程中出现错误，会显示具体的错误信息
- 导出成功后会显示文件保存路径

## 后续优化建议

1. **图片嵌入**: 可以考虑将原始图片直接嵌入到Excel文件中
2. **格式美化**: 可以添加更多的格式设置，如边框、颜色等
3. **导出选项**: 可以提供更多导出选项，如选择导出字段等
4. **文件分享**: 可以添加文件分享功能，方便用户传输文件
