# LYA-1000 Kiosk模式部署指南

## 概述

本指南详细说明如何将LYA-1000数据采集仪应用配置为kiosk模式，实现开机自启动、全屏运行、防止用户退出等工业应用需求。

## 功能特性

### ✅ 已实现的Kiosk模式功能

1. **全屏显示**
   - 自动隐藏状态栏和导航栏
   - 强制横屏模式
   - 沉浸式界面体验

2. **退出防护**
   - 禁用返回键、Home键、菜单键
   - 禁用任务切换功能
   - 防止用户意外退出应用

3. **管理员退出机制**
   - 连续点击7次触发密码输入
   - 管理员密码：`LYA1000ADMIN`
   - 双重确认机制

4. **设备管理员权限**
   - 锁定任务模式
   - 设备级别的应用保护
   - 防止卸载和禁用

5. **开机自启动**
   - 系统启动后自动运行
   - 后台服务保活机制
   - 异常恢复功能

6. **管理员设置页面**
   - 可视化管理界面
   - 实时状态监控
   - 便捷的开关控制

## 部署步骤

### 第一步：编译应用

```bash
# 清理项目
flutter clean

# 获取依赖
flutter pub get

# 编译Release版本
flutter build apk --release
```

编译完成后，APK文件位于：`build/app/outputs/flutter-apk/app-release.apk`

### 第二步：安装应用

1. **通过ADB安装**
```bash
adb install build/app/outputs/flutter-apk/app-release.apk
```

2. **通过文件管理器安装**
   - 将APK文件复制到设备
   - 使用文件管理器打开并安装
   - 允许未知来源应用安装

### 第三步：配置设备管理员

#### 方法A：标准设备管理员模式（会有系统提示）

1. **启动应用**
   - 首次启动会自动提示启用设备管理员
   - 点击"激活"按钮

2. **手动启用（如果需要）**
   - 进入系统设置 → 安全 → 设备管理员
   - 找到"LYA-1000数据采集仪"
   - 启用设备管理员权限

#### 方法B：设备所有者模式（无系统提示，推荐）

⚠️ **注意：** 此方法需要在全新设备或恢复出厂设置后的设备上操作

1. **通过ADB设置设备所有者**
   ```bash
   # 安装应用
   adb install app-release.apk

   # 设置为设备所有者
   adb shell dpm set-device-owner com.example.a325_lya_1000/.DeviceAdminReceiver
   ```

2. **验证设置**
   ```bash
   # 检查设备所有者状态
   adb shell dpm list-owners
   ```

详细设置步骤请参考：`setup_device_owner.md`

### 第四步：配置自启动权限

1. **系统自启动设置**
   - 进入系统设置 → 应用管理
   - 找到"LYA-1000数据采集仪"
   - 启用"自启动"权限

2. **电池优化设置**
   - 进入系统设置 → 电池 → 电池优化
   - 找到应用并设置为"不优化"

3. **后台应用限制**
   - 确保应用不被后台清理
   - 加入系统白名单

### 第五步：验证功能

1. **重启设备测试**
   - 重启设备
   - 观察应用是否自动启动
   - 检查是否进入kiosk模式

2. **功能测试**
   - 尝试按返回键、Home键
   - 确认无法退出应用
   - 测试管理员退出功能

## 管理员操作指南

### 退出Kiosk模式

1. **手势退出**
   - 在主界面连续快速点击7次
   - 输入管理员密码：`LYA1000ADMIN`
   - 确认退出

2. **管理员设置页面**
   - 通过正常导航进入管理员设置
   - 使用开关控制kiosk模式

### 重新启用Kiosk模式

1. **通过设置页面**
   - 进入管理员设置页面
   - 开启kiosk模式开关
   - 开启设备管理员开关

2. **重启应用**
   - 应用会自动检测并启用kiosk模式

## 关于"App is pinned"系统提示

### 问题描述
应用启动时可能会显示"App is pinned"系统提示，影响用户体验。

### 解决方案

#### 方案1：设备所有者模式（推荐）
- 将应用设置为设备所有者可完全避免系统提示
- 需要在全新设备上操作
- 详细步骤请参考 `setup_device_owner.md`

#### 方案2：自动处理提示
- 应用已集成自动处理逻辑
- 延迟启动锁定任务模式
- 自动关闭系统对话框

#### 方案3：手动处理
- 用户首次看到提示时点击"GOT IT"
- 后续启动将不再显示提示

## 故障排除

### 常见问题及解决方案

#### 问题1：应用无法自启动
**症状：** 设备重启后应用没有自动启动

**解决方案：**
1. 检查自启动权限是否开启
2. 确认应用未被电池优化
3. 检查系统是否有第三方安全软件阻止
4. 重新安装应用

#### 问题2：无法进入kiosk模式
**症状：** 应用启动但仍可以退出

**解决方案：**
1. 确认设备管理员权限已启用
2. 检查Android版本兼容性（建议Android 5.0+）
3. 重启应用或设备
4. 通过管理员设置页面手动启用

#### 问题3：管理员退出不工作
**症状：** 连续点击无反应或密码错误

**解决方案：**
1. 确保快速连续点击7次（3秒内）
2. 检查密码输入：`LYA1000ADMIN`（区分大小写）
3. 重启应用重试

#### 问题4：应用被系统杀死
**症状：** 应用运行一段时间后自动关闭

**解决方案：**
1. 将应用加入系统白名单
2. 关闭电池优化
3. 检查内存管理设置
4. 确认后台服务正常运行

### 日志调试

如果遇到问题，可以通过以下方式获取日志：

```bash
# 查看应用日志
adb logcat | grep "LYA1000\|KioskManager\|BootReceiver"

# 查看系统日志
adb logcat | grep "DeviceAdmin\|ActivityManager"
```

## 安全注意事项

### 密码安全
- 管理员密码：`LYA1000ADMIN`
- 建议在生产环境中修改默认密码
- 密码修改位置：`lib/utils/kiosk_manager.dart` 第214行

### 权限管理
- 设备管理员权限具有较高的系统权限
- 仅在可信设备上启用
- 定期检查权限状态

### 数据安全
- 应用数据存储在设备本地
- 建议定期备份重要数据
- 确保设备物理安全

## 维护建议

### 定期检查项目
1. **每周检查**
   - 应用运行状态
   - 设备管理员权限
   - 自启动功能

2. **每月检查**
   - 系统更新影响
   - 应用性能监控
   - 存储空间使用

3. **季度检查**
   - 应用版本更新
   - 安全补丁安装
   - 硬件状态检查

### 更新流程
1. 备份当前应用数据
2. 测试新版本兼容性
3. 在测试设备上验证功能
4. 逐步部署到生产设备
5. 监控部署后状态

## 技术支持

### 联系信息
- 技术支持：请联系开发团队
- 文档更新：请参考项目README

### 相关文档
- `test_kiosk_mode.md` - 详细测试指南
- `README.md` - 项目总体说明
- `lib/utils/kiosk_manager.dart` - Kiosk模式核心代码

## 版本信息

- **当前版本：** 1.0.0
- **支持的Android版本：** 5.0+ (API Level 21+)
- **推荐的Android版本：** 8.0+ (API Level 26+)
- **最后更新：** 2025-01-13

---

**注意：** 本指南基于当前实现的功能编写。如有功能更新或修改，请及时更新本文档。
