# 串口GPS服务使用说明

## 概述

串口GPS服务 (`SerialGPSService`) 用于读取连接到 `/dev/ttyUSB3` 串口的高精度GPS模块数据。该服务支持解析标准NMEA格式的GPS数据，并提供实时的经纬度、高度等位置信息。

## 硬件要求

- **GPS模块**: 支持NMEA 0183协议的GPS模块
- **串口连接**: GPS模块连接到 `/dev/ttyUSB3`
- **通信参数**:
  - 波特率: 115200
  - 数据位: 8
  - 停止位: 1
  - 校验位: 无
  - 流控制: 无

## 支持的NMEA语句

| 语句类型 | 描述 | 解析内容 |
|---------|------|----------|
| GGA | 全球定位系统固定数据 | 纬度、经度、高度、定位质量、卫星数量 |
| RMC | 推荐最小定位信息 | 纬度、经度、时间、速度、航向 |
| GSV | 可见卫星信息 | 卫星数量、信号强度 |

## 使用方法

### 1. 基本使用

```dart
import '../service/serial_gps_service.dart';

// 获取服务实例
final gpsService = SerialGPSService.instance;

// 添加数据监听器
gpsService.addListener((gpsData) {
  print('GPS数据更新: $gpsData');
  print('纬度: ${gpsData.latitude}');
  print('经度: ${gpsData.longitude}');
  print('高度: ${gpsData.altitude}');
});

// 连接串口
final connected = await gpsService.connect();
if (connected) {
  print('GPS连接成功');
} else {
  print('GPS连接失败');
}

// 断开连接
await gpsService.disconnect();
```

### 2. 集成到现有GPS服务

```dart
import '../service/gps_service.dart';

final gpsService = GPSService.instance;

// 启用串口GPS
final success = await gpsService.enableSerialGPS();
if (success) {
  print('串口GPS已启用');
  
  // 获取最新数据
  final data = gpsService.getSerialGPSData();
  if (data != null) {
    print('当前位置: ${data.latitude}, ${data.longitude}');
  }
}

// 禁用串口GPS
await gpsService.disableSerialGPS();
```

### 3. 在UI中使用

参考 `lib/examples/serial_gps_usage_example.dart` 文件，该文件展示了如何在Flutter界面中集成串口GPS功能。

## GPS数据模型

```dart
class GPSData {
  final double latitude;        // 纬度（十进制度）
  final double longitude;       // 经度（十进制度）
  final double? altitude;       // 海拔高度（米）
  final String? fixQuality;     // 定位质量描述
  final int? satelliteCount;    // 卫星数量
  final DateTime timestamp;     // 数据时间戳
}
```

## 定位质量说明

| 质量代码 | 描述 | 精度 |
|---------|------|------|
| 0 | 无效 | 无定位 |
| 1 | GPS定位 | 3-5米 |
| 2 | DGPS定位 | 1-3米 |
| 3 | PPS定位 | 亚米级 |
| 4 | RTK固定解 | 厘米级 |
| 5 | RTK浮点解 | 分米级 |
| 6 | 估算 | 低精度 |
| 7 | 手动输入 | 人工设置 |
| 8 | 模拟模式 | 测试模式 |

## 错误处理

### 常见问题及解决方案

1. **串口设备不存在**
   ```
   错误: 串口设备 /dev/ttyUSB3 不存在
   解决: 检查GPS模块是否正确连接，确认设备路径
   ```

2. **权限不足**
   ```
   错误: Permission denied
   解决: 确保应用有访问串口设备的权限
   ```

3. **数据解析错误**
   ```
   错误: 校验和错误，忽略数据
   解决: 检查GPS模块输出格式，确认NMEA协议兼容性
   ```

4. **连接超时**
   ```
   错误: 连接超时
   解决: 检查波特率设置，确认GPS模块正常工作
   ```

## 调试和测试

### 1. 使用测试工具

```dart
import '../utils/serial_gps_test.dart';

// 运行完整测试
await runSerialGPSTest();

// 测试NMEA解析器
runNMEAParserTest();
```

### 2. 手动测试串口

在Linux终端中测试串口连接：

```bash
# 检查设备是否存在
ls -l /dev/ttyUSB*

# 配置串口参数
stty -F /dev/ttyUSB3 115200 cs8 -cstopb -parenb raw

# 读取数据
cat /dev/ttyUSB3
```

### 3. 查看日志输出

启用详细日志输出来调试问题：

```dart
// 在连接前启用调试模式
print('[调试] 开始连接串口GPS');
final connected = await gpsService.connect();
print('[调试] 连接结果: $connected');
```

## 性能优化

1. **数据缓存**: 服务会缓存最新的GPS数据，避免重复解析
2. **异步处理**: 所有串口操作都是异步的，不会阻塞UI线程
3. **资源管理**: 自动管理串口连接和数据流，防止内存泄漏
4. **错误恢复**: 内置错误处理和自动重连机制

## 注意事项

1. **设备权限**: 确保应用有访问串口设备的权限
2. **硬件兼容**: 确认GPS模块支持NMEA 0183协议
3. **波特率匹配**: GPS模块的波特率必须设置为115200
4. **数据格式**: 确保GPS模块输出标准NMEA格式数据
5. **资源释放**: 使用完毕后及时断开连接，释放系统资源

## 示例NMEA数据

```
$GPGGA,123519,4807.038,N,01131.000,E,1,08,0.9,545.4,M,46.9,M,,*47
$GPRMC,123519,A,4807.038,N,01131.000,E,022.4,084.4,230394,003.1,W*6A
$GPGSV,2,1,08,01,40,083,46,02,17,308,41,12,07,344,39,14,22,228,45*75
```

解析结果：
- 纬度: 48.1173°N
- 经度: 11.5167°E  
- 高度: 545.4米
- 定位质量: GPS定位
- 卫星数量: 8颗

## 技术支持

如果遇到问题，请检查：
1. 硬件连接是否正确
2. 设备权限是否充足
3. GPS模块是否正常工作
4. NMEA数据格式是否标准

更多技术细节请参考源码注释和示例代码。
