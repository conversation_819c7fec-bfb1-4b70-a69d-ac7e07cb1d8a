import 'package:a325_lya_1000/pages/data_processing_page.dart';
import 'package:flutter/material.dart';
import 'package:get_storage/get_storage.dart';
import 'package:google_mlkit_text_recognition/google_mlkit_text_recognition.dart';
import 'dart:io';
import 'package:image_picker/image_picker.dart';
import '../utils/image_utils.dart';
import '../service/ocr_service.dart';
import '../service/gps_service.dart';
import '../service/ollama_vision_service.dart';
import '../theme/industrial_theme.dart';
import '../widgets/industrial_widgets.dart';
import '../utils/error_handler.dart';

import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart' as p; // 给 path 包起别名 p;

import 'package:camera/camera.dart'; // 新增相机依赖
import 'package:image/image.dart' as img;

final gpsService = GPSService.instance;

/// 数据采集页面，适配7寸1024x600分辨率屏幕
/// 页面布局：
/*
  - 顶部左上角显示当前项目名称（大字体，醒目）
  - 主体分为两大部分：
    1. 图片及识别数据展示区（左侧或上半部分）
       - 显示3张图片：
         * 相机拍照原始图
         * 两张裁剪后的图片
       - 显示对应的图片识别数字信息（3个数字，分别对应3张图片）
    2. GPS信息展示区（右侧或下半部分）
       - 显示6个GPS相关数据：
         * 经度、纬度、高度
         * 最近三次临近采集点之间的距离（3个距离值）
  - 底部固定两个按钮：
    * 数据采集（触发拍照、GPS采集等）
    * 数据存储（保存当前采集数据）

  设计考虑：
  - 使用Flexible和Expanded布局，保证在1024x600分辨率下合理分配空间
  - 图片展示区使用Grid或Row+Column组合，保证图片大小适中
  - 文字信息使用ListTile或简单Text+Row布局，清晰展示
  - 按钮使用宽度适中、易点击的ElevatedButton
*/

class DataCollectionPage extends StatefulWidget {
  final String projectName;
  final String projectUniqueId;

  const DataCollectionPage({
    Key? key,
    required this.projectUniqueId,
    required this.projectName,
  }) : super(key: key);

  @override
  _DataCollectionPageState createState() => _DataCollectionPageState();
}

class _DataCollectionPageState extends State<DataCollectionPage> {
  final ImagePicker _picker = ImagePicker();
  final GetStorage _storage = GetStorage();
  late Database _db;
  bool _isProcessing = false; // 新增状态变量
  bool _isObserving = false; // 新增：是否处于设备观测状态

  CameraController? _cameraController; // 相机控制器
  List<CameraDescription>? _cameras;

  // 模拟图片数据，实际应替换为Image.file或Image.memory等
  Image? originalImage;
  Image? croppedImage1;
  Image? croppedImage2;

  // 模拟识别数字
  String originalImageNumber = '-';
  String croppedImage1Number = '-';
  String croppedImage2Number = '-';

  // 数值输入控制器
  late TextEditingController originalNumberController;
  late TextEditingController croppedNumber1Controller;
  late TextEditingController croppedNumber2Controller;

  // 原始图片路径
  String? originalImagePath;

  // GPS信息
  double? latitude;
  double? longitude;
  double? altitude;
  double? accuracy; // GPS精度
  String gpsStatus = '未获取'; // GPS状态
  int? satelliteCount; // 卫星数量
  String? fixQuality; // 定位质量

  // 最近三次临近采集点距离（单位米）
  List<double> recentDistances = [-1, -1, -1];

  // 采集点距离（上一采集点到当前位置的距离）
  double collectionPointDistance = -1;

  // GPS服务实例
  final GPSService _gpsService = GPSService.instance;

  // Ollama视觉识别服务实例
  final OllamaVisionService _visionService = OllamaVisionService.instance;

  @override
  void initState() {
    super.initState();
    // 初始化控制器
    originalNumberController = TextEditingController(text: originalImageNumber);
    croppedNumber1Controller = TextEditingController(text: croppedImage1Number);
    croppedNumber2Controller = TextEditingController(text: croppedImage2Number);

    _initDatabase();
    _initCamera();
    _initSerialGPS();
  }

  Future<void> _initSerialGPS() async {
    try {
      // 启用串口GPS
      final success = await _gpsService.enableSerialGPS();
      if (mounted) {
        setState(() {
          gpsStatus = success ? '串口GPS已启用' : '串口GPS启用失败';
        });
      }

      if (success) {
        print('串口GPS已启用');
        // 开始监听GPS数据更新
        _startGPSDataListener();
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          gpsStatus = 'GPS初始化失败: $e';
        });
      }
    }
  }

  /// 开始监听GPS数据更新
  void _startGPSDataListener() {
    // 每秒检查一次GPS数据更新
    Stream.periodic(Duration(seconds: 1)).listen((_) {
      if (mounted) {
        _updateSerialGPSData();
      }
    });
  }

  Future<void> _initCamera() async {
    try {
      // 先释放现有的相机控制器
      if (_cameraController != null) {
        await _cameraController!.dispose();
        _cameraController = null;
      }

      _cameras = await availableCameras();
      if (_cameras != null && _cameras!.isNotEmpty) {
        // 使用最高分辨率设置，目标1440x1080或更高
        _cameraController = CameraController(
          _cameras![0],
          ResolutionPreset.veryHigh, // 使用veryHigh获得最高分辨率
          enableAudio: false,
          imageFormatGroup: ImageFormatGroup.jpeg,
        );

        await _cameraController!.initialize();

        // 设置相机为全自动模式
        await _setupCameraAutoMode();

        print('相机初始化成功');
        print('相机分辨率: ${_cameraController!.value.previewSize}');

        if (mounted) setState(() {});
      }
    } catch (e) {
      print('相机初始化失败: $e');
      // 相机初始化失败时，确保控制器为null
      _cameraController = null;
      if (mounted) setState(() {});
    }
  }

  /// 设置相机为全自动模式
  Future<void> _setupCameraAutoMode() async {
    if (_cameraController == null || !_cameraController!.value.isInitialized) {
      return;
    }

    try {
      // 设置自动聚焦模式
      await _cameraController!.setFocusMode(FocusMode.auto);

      // 设置自动曝光模式
      await _cameraController!.setExposureMode(ExposureMode.auto);

      // 启用闪光灯自动模式
      await _cameraController!.setFlashMode(FlashMode.auto);

      print('相机自动模式设置完成');
      print('聚焦模式: 自动');
      print('曝光模式: 自动');
      print('闪光灯: 自动');

    } catch (e) {
      print('设置相机自动模式失败: $e');
      // 即使某些设置失败，也继续使用相机
    }
  }



  @override
  void dispose() {
    _cameraController?.dispose();
    originalNumberController.dispose();
    croppedNumber1Controller.dispose();
    croppedNumber2Controller.dispose();
    super.dispose();
  }

  Future<void> _initDatabase() async {
    final dbPath = await getDatabasesPath();
    _db = await openDatabase(
      p.join(dbPath, 'data_processing.db'),
      version: 1,
      onCreate: (db, version) async {
        // 不创建表，表由项目名称动态创建
      },
    );
    await _createTableIfNotExists();
  }

  Future<void> _createTableIfNotExists() async {
    final tableName = widget.projectUniqueId; // 这里用项目名作为表名，或改为唯一ID
    await _db.execute('''
      CREATE TABLE IF NOT EXISTS `$tableName` (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        distance REAL,
        mileage TEXT,
        current REAL,
        depth REAL,
        db REAL,
        description TEXT,
        latitude REAL,
        longitude REAL,
        altitude REAL
      )
    ''');
  }

  // 图像处理方法（优化内存使用）
  Future<Map<String, dynamic>> _processImageInBackground(File photoFile) async {
    try {
      // 1. 使用Ollama视觉识别原始图片数字
      String originalDigits = '-';
      try {
        // originalDigits = await _visionService.recognizeNumbers(photoFile);
        print('Ollama识别原始图片数字：$originalDigits');
      } catch (e) {
        print('Ollama识别失败，使用备用OCR: $e');
        // 备用方案：使用本地OCR
        final inputImage = InputImage.fromFile(photoFile);
        originalDigits = await OCRService.processImageBymlkit(inputImage);
        print('本地OCR识别原始图片数字：$originalDigits');
      }

      // 2. 裁剪左下角仪表数字区域
      print('=== 裁剪区域1 ===');
      print('裁剪参数: x=80, y=820, w=480, h=300');
      File? croppedFile1 = await ImageUtils.cropMeterDigits(
        photoFile,
        80,
        820,
        480,
        300,
      );

      String croppedDigits1 = '-';
      if (croppedFile1 != null) {
        // 检查裁剪后的图片尺寸
        final croppedBytes1 = await croppedFile1.readAsBytes();
        final croppedImg1 = img.decodeImage(croppedBytes1);
        if (croppedImg1 != null) {
          print('裁剪后图片1尺寸: ${croppedImg1.width} x ${croppedImg1.height}');
          print('裁剪后图片1路径: ${croppedFile1.path}');
        }

        try {
          // 优先使用Ollama识别
          print("ollama 方案识别-1："+croppedFile1.path);
          croppedDigits1 = await _visionService.recognizeNumbers(croppedFile1);
        } catch (e) {
          // 备用方案：使用本地OCR
          print("本地OCR 方案识别-1："+croppedFile1.path);
          final inputImage_1 = InputImage.fromFile(croppedFile1);
          croppedDigits1 = await OCRService.processImageBymlkit(inputImage_1);
        }
      } else {
        print('裁剪区域1失败：croppedFile1 为 null');
      }

      // 3. 裁剪右下角区域
      print('=== 裁剪区域2 ===');
      print('裁剪参数: x=800, y=720, w=480, h=300');
      File? croppedFile2 = await ImageUtils.cropMeterDigits(
        photoFile,
        1300,
        820,
        480,
        300,
      );

      String croppedDigits2 = '-';
      if (croppedFile2 != null) {
        // 检查裁剪后的图片尺寸
        final croppedBytes2 = await croppedFile2.readAsBytes();
        final croppedImg2 = img.decodeImage(croppedBytes2);
        if (croppedImg2 != null) {
          print('裁剪后图片2尺寸: ${croppedImg2.width} x ${croppedImg2.height}');
          print('裁剪后图片2路径: ${croppedFile2.path}');
        }

        try {
          print("ollama 方案识别-2："+croppedFile2.path);
          // 优先使用Ollama识别
          croppedDigits2 = await _visionService.recognizeNumbers(croppedFile2);
        } catch (e) {
          // 备用方案：使用本地OCR
          print("本地OCR 方案识别-2："+croppedFile2.path);
          final inputImage_2 = InputImage.fromFile(croppedFile2);
          croppedDigits2 = await OCRService.processImageBymlkit(inputImage_2);
        }
      } else {
        print('裁剪区域2失败：croppedFile2 为 null');
      }

      return {
        'originalDigits': originalDigits,
        'croppedDigits1': croppedDigits1,
        'croppedDigits2': croppedDigits2,
        'croppedFile1Path': croppedFile1?.path,
        'croppedFile2Path': croppedFile2?.path,
      };
    } catch (e) {
      print('图像处理失败: $e');
      return {
        'originalDigits': '-',
        'croppedDigits1': '-',
        'croppedDigits2': '-',
        'croppedFile1Path': null,
        'croppedFile2Path': null,
      };
    }
  }

  // 更新串口GPS数据
  void _updateSerialGPSData() {
    try {
      final data = _gpsService.getSerialGPSData();

      if (data != null) {
        setState(() {
          latitude = data.latitude;
          longitude = data.longitude;
          altitude = data.altitude;
          satelliteCount = data.satelliteCount;
          fixQuality = data.fixQuality;

          // 根据定位质量设置GPS状态
          if (data.fixQuality != null) {
            switch (data.fixQuality) {
              case 'DGPS定位':
                gpsStatus = '已接收';
                accuracy = 1.0; // DGPS精度约1-3米
                break;
              case '标准':
                gpsStatus = '已接收';
                accuracy = 3.0; // 标准GPS精度约3-5米
                break;
              default:
                gpsStatus = '已接收';
                accuracy = 5.0;
            }
          } else {
            gpsStatus = '已接收';
            accuracy = null;
          }

          // 记录GPS位置用于距离计算
          _gpsService.recordSerialGPSPosition(data);

          // 获取最近三次采集点距离
          recentDistances = _gpsService.getRecentDistances();

          // 获取采集点距离（上一采集点到当前位置的距离）
          collectionPointDistance = _gpsService.getSerialGPSRecentDistance();
        });
      }
    } catch (e) {
      // 静默处理错误，避免频繁日志
    }
  }

  // 采集数据方法 - 直接使用相机控制器拍照
  Future<void> collectData() async {
    // 防止重复点击
    if (_isProcessing) return;

    // 检查相机是否可用
    if (_cameraController == null || !_cameraController!.value.isInitialized) {
      IndustrialErrorHandler.showError(context, '相机未就绪，请稍后再试');
      return;
    }

    setState(() {
      _isProcessing = true; // 开始处理，显示进度动画
    });

    try {
      // 1. 直接使用相机控制器拍照
      final XFile photo = await _cameraController!.takePicture();
      File photoFile = File(photo.path);

      // 打印原始照片存储路径
      print('=== 原始照片信息 ===');
      print('原始照片路径: ${photoFile.path}');
      print('原始照片大小: ${await photoFile.length()} bytes');

      // 检查原始图片尺寸
      final bytes = await photoFile.readAsBytes();
      final decodedImage = img.decodeImage(bytes);
      if (decodedImage != null) {
        print('原始图片尺寸: ${decodedImage.width} x ${decodedImage.height}');
      }

      IndustrialErrorHandler.showSuccess(context, '照片拍摄成功，正在进行图像识别...');

      // 2. 在后台线程进行图像处理，避免阻塞UI
      final results = await _processImageInBackground(photoFile);

      // 3. 更新界面显示图片和识别结果
      if (mounted) {
        setState(() {
          originalImage = Image.file(photoFile);

          // 从后台处理结果中获取数据
          final originalDigits = results['originalDigits'] ?? '-';
          final croppedDigits1 = results['croppedDigits1'] ?? '-';
          final croppedDigits2 = results['croppedDigits2'] ?? '-';
          final croppedFile1Path = results['croppedFile1Path'];
          final croppedFile2Path = results['croppedFile2Path'];

          // 调试信息
          print('croppedFile1Path: $croppedFile1Path');
          print('croppedFile2Path: $croppedFile2Path');

          croppedImage1 = croppedFile1Path != null ? Image.file(File(croppedFile1Path)) : null;
          croppedImage2 = croppedFile2Path != null ? Image.file(File(croppedFile2Path)) : null;

          print('croppedImage1 created: ${croppedImage1 != null}');
          print('croppedImage2 created: ${croppedImage2 != null}');

          originalImageNumber = originalDigits.isNotEmpty ? originalDigits : '-';
          croppedImage1Number = croppedDigits1.isNotEmpty ? croppedDigits1 : '-';
          croppedImage2Number = croppedDigits2.isNotEmpty ? croppedDigits2 : '-';

          // 只在OCR识别后更新控制器的值
          originalNumberController.text = originalImageNumber;
          croppedNumber1Controller.text = croppedImage1Number;
          croppedNumber2Controller.text = croppedImage2Number;

          // 保存原始图片路径
          originalImagePath = photoFile.path;

          // 获取真实GPS数据
          _updateSerialGPSData();
        });
      }
    } catch (e) {
      print('采集数据失败: $e');
      if (mounted) {
        IndustrialErrorHandler.showError(context, '采集数据失败: $e');
      }
    } finally {
      // 不需要重新初始化相机，直接结束处理状态
      if (mounted) {
        setState(() {
          _isProcessing = false; // 结束处理，隐藏进度动画
        });
      }
    }

    // setState(() {
    //   // 模拟赋值
    //   originalImage = Image.asset('assets/sample_original.jpg');
    //   croppedImage1 = Image.asset('assets/sample_crop1.jpg');
    //   croppedImage2 = Image.asset('assets/sample_crop2.jpg');
    //
    //   originalImageNumber = '123';
    //   croppedImage1Number = '45';
    //   croppedImage2Number = '67';
    //
    //   latitude = 39.9042;
    //   longitude = 116.4074;
    //   altitude = 50.0;
    //
    //   recentDistances = [10.5, 8.2, 12.3];
    // });
  }

  // 模拟存储数据方法
  // void saveData() {
  //   // TODO: 实现数据存储逻辑
  //   ScaffoldMessenger.of(
  //     context,
  //   ).showSnackBar(SnackBar(content: Text('数据已保存')));
  // }

  void saveData() async {
    final tableName = widget.projectUniqueId;
    try {
      // 从控制器获取用户输入的值，而不是从变量获取
      // 这样可以保存用户修改后的值
      double currentValue = double.tryParse(croppedNumber1Controller.text) ?? 0;
      double depthValue = double.tryParse(croppedNumber2Controller.text) ?? 0;

      await _db.insert('`$tableName`', {
        'distance': 0, // 模拟
        'mileage': '模拟里程',
        'current': currentValue,
        'depth': depthValue,
        'db': 0, // 模拟
        'description': '模拟描述',
        'latitude': latitude ?? 0,
        'longitude': longitude ?? 0,
        'altitude': altitude ?? 0,
        'original_image_path': originalImagePath ?? '', // 添加原始图片路径
      });

      if (mounted) {
        IndustrialErrorHandler.showSuccess(context, '数据已成功保存到数据库');
      }
    } catch (e) {
      if (mounted) {
        IndustrialErrorHandler.showError(context, '保存数据失败: $e');
      }
    }
  }

  Widget buildImageWithLabel(
    String label,
    Image? image,
    String number,
    double w,
    double h,
    TextEditingController controller,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 图片显示区域
        Container(
          width: w,
          height: h,
          decoration: BoxDecoration(
            color: Colors.grey[300],
            border: Border.all(color: Colors.grey[400]!, width: 1),
            borderRadius: BorderRadius.circular(4),
          ),
          child: number == 'null'
              ? _buildCameraPreviewWithOverlay(w, h) // 原始图片显示相机预览
              : _buildCroppedImageDisplay(image, w, h), // 裁剪图片显示实际图片
        ),
        SizedBox(height: 8),
        // 标签和输入区域
        if (number == 'null') ...[
          Text(
            label,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
              color: IndustrialTheme.textPrimary,
            ),
          ),
          SizedBox(height: 4),
          Text(
            '实时相机预览 - 红框显示识别区域',
            style: TextStyle(
              fontSize: 12,
              color: IndustrialTheme.textSecondary,
            ),
          ),
        ] else ...[
          Text(
            label,
            style: TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 14,
              color: IndustrialTheme.textPrimary,
            ),
          ),
          SizedBox(height: 6),
          Row(
            children: [
              Text(
                '数值识别:',
                style: TextStyle(
                  fontSize: 13,
                  color: IndustrialTheme.textSecondary,
                ),
              ),
              SizedBox(width: 8),
              Expanded(
                child: Container(
                  height: 36,
                  child: TextField(
                    controller: controller,
                    textAlign: TextAlign.center,
                    decoration: InputDecoration(
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(4),
                      ),
                      contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                      isDense: true,
                      filled: true,
                      fillColor: Colors.white,
                    ),
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: IndustrialTheme.primaryBlue,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  /// 构建裁剪图片显示组件
  Widget _buildCroppedImageDisplay(Image? image, double width, double height) {
    if (image == null) {
      return Container(
        width: width,
        height: height,
        color: Colors.grey[200],
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.image_not_supported, color: Colors.grey[600], size: 20),
              SizedBox(height: 4),
              Text('无图片', style: TextStyle(color: Colors.grey[600], fontSize: 10)),
            ],
          ),
        ),
      );
    }

    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[400]!, width: 1),
      ),
      child: ClipRect(
        child: FittedBox(
          fit: BoxFit.contain, // 保持图片比例，完整显示
          child: image,
        ),
      ),
    );
  }

  /// 构建带有裁剪区域叠加的相机预览
  Widget _buildCameraPreviewWithOverlay(double width, double height) {
    // 检查相机控制器状态，防止在释放后使用
    if (_cameraController == null ||
        _cameraController!.value.isInitialized == false ||
        !mounted) {
      return Container(
        width: width,
        height: height,
        color: Colors.grey[300],
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(IndustrialTheme.primaryBlue),
              ),
              SizedBox(height: 8),
              Text('相机初始化中...', style: TextStyle(fontSize: 12)),
            ],
          ),
        ),
      );
    }

    try {
      // 获取相机预览尺寸
      final previewSize = _cameraController!.value.previewSize;
      if (previewSize == null) {
        return Container(
          width: width,
          height: height,
          color: Colors.grey[300],
          child: Center(child: Text('相机预览不可用')),
        );
      }

      final aspectRatio = previewSize.width / previewSize.height;

      // 计算适合容器的尺寸，保持相机原始比例
      double previewWidth, previewHeight;
      final containerAspectRatio = width / height;

      if (aspectRatio > containerAspectRatio) {
        // 相机比容器更宽，以宽度为准
        previewWidth = width;
        previewHeight = width / aspectRatio;
      } else {
        // 相机比容器更高，以高度为准
        previewHeight = height;
        previewWidth = height * aspectRatio;
      }

      return ClipRect(
        child: Container(
          width: width,
          height: height,
          color: Colors.grey[300],
          child: Stack(
            alignment: Alignment.center,
            children: [
              // 相机预览 - 旋转90度并镜像处理
              Transform.rotate(
                angle: -4.7408, // -90度 (π/2 弧度)
                child: Transform(
                  alignment: Alignment.center,
                  transform: Matrix4.identity()..scale(-1.0, 1.0), // 水平镜像
                  child: SizedBox(
                    width: previewHeight, // 注意：旋转后宽高互换
                    height: previewWidth,
                    child: CameraPreview(_cameraController!),
                  ),
                ),
              ),
              // 裁剪区域叠加 - 基于旋转后的预览尺寸
              _buildCropOverlay(width, height),
            ],
          ),
        ),
      );
    } catch (e) {
      print('相机预览构建错误: $e');
      return Container(
        width: width,
        height: height,
        color: Colors.grey[300],
        child: Center(child: Text('相机预览错误')),
      );
    }
  }

  /// 构建裁剪区域叠加
  Widget _buildCropOverlay(double previewWidth, double previewHeight) {
    // 获取实际相机分辨率
    if (_cameraController == null || !_cameraController!.value.isInitialized) {
      return Container();
    }

    final cameraSize = _cameraController!.value.previewSize!;
    final double actualImageWidth = cameraSize.width;
    final double actualImageHeight = cameraSize.height;

    print('相机实际分辨率: $actualImageWidth x $actualImageHeight');
    print('预览显示尺寸: $previewWidth x $previewHeight');

    // 由于相机预览已旋转90度并镜像处理，需要调整坐标计算
    // 原始裁剪坐标是基于未旋转未镜像的图像，现在需要转换到旋转+镜像后的坐标系

    // 原始裁剪区域坐标（基于未旋转的图像）
    // croppedFile1: (80, 820, 480, 300)
    // croppedFile2: (800, 720, 480, 300)

    // 旋转90度 + 镜像后的坐标转换：
    // 1. 先旋转90度：新X = 原Y, 新Y = 原图宽度 - 原X - 原宽度
    // 2. 再镜像：镜像X = 预览宽度 - 旋转后X - 旋转后宽度

    // croppedFile1 裁剪区域转换
    final double rotatedCrop1X = (820 / actualImageHeight) * previewWidth;
    final double rotatedCrop1Y = (actualImageWidth - 80 - 480) * (previewHeight / actualImageWidth);
    final double rotatedCrop1W = (300 / actualImageHeight) * previewWidth;
    final double rotatedCrop1H = (480 / actualImageWidth) * previewHeight;

    // 镜像处理
    final double crop1X = previewWidth - rotatedCrop1X - rotatedCrop1W;
    final double crop1Y = rotatedCrop1Y;
    final double crop1W = rotatedCrop1W;
    final double crop1H = rotatedCrop1H;

    // croppedFile2 裁剪区域转换
    final double rotatedCrop2X = (720 / actualImageHeight) * previewWidth;
    final double rotatedCrop2Y = (actualImageWidth - 800 - 480) * (previewHeight / actualImageWidth);
    final double rotatedCrop2W = (300 / actualImageHeight) * previewWidth;
    final double rotatedCrop2H = (480 / actualImageWidth) * previewHeight;

    // 镜像处理
    final double crop2X = previewWidth - rotatedCrop2X - rotatedCrop2W;
    final double crop2Y = rotatedCrop2Y;
    final double crop2W = rotatedCrop2W;
    final double crop2H = rotatedCrop2H;

    print('旋转+镜像后裁剪区域1预览位置: x=${crop1X.toInt()}, y=${crop1Y.toInt()}, w=${crop1W.toInt()}, h=${crop1H.toInt()}');
    print('旋转+镜像后裁剪区域2预览位置: x=${crop2X.toInt()}, y=${crop2Y.toInt()}, w=${crop2W.toInt()}, h=${crop2H.toInt()}');

    return Stack(
      children: [
        // croppedFile1 红色方框 - 对应右侧上方显示区域
        Positioned(
          left: crop1X,
          top: crop1Y,
          child: Container(
            width: crop1W,
            height: crop1H,
            decoration: BoxDecoration(
              border: Border.all(
                color: Colors.red,
                width: 2,
              ),
            ),
            child: Align(
              alignment: Alignment.topLeft,
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                color: Colors.red,
                child: Text(
                  '区域1',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ),
        ),
        // croppedFile2 红色方框 - 对应右侧下方显示区域
        Positioned(
          left: crop2X,
          top: crop2Y,
          child: Container(
            width: crop2W,
            height: crop2H,
            decoration: BoxDecoration(
              border: Border.all(
                color: Colors.red,
                width: 2,
              ),
            ),
            child: Align(
              alignment: Alignment.topLeft,
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                color: Colors.red,
                child: Text(
                  '区域2',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget buildGpsInfoItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          SizedBox(
            width: 120,
            child: Text(label, style: TextStyle(fontWeight: FontWeight.w600)),
          ),
          Text(value),
        ],
      ),
    );
  }

  /// 构建工业级图像处理进度提示界面
  Widget _buildProcessingOverlay() {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: IndustrialStyles.spacingM),
      child: Card(
        elevation: 6,
        shadowColor: IndustrialTheme.processingPurple.withValues(alpha: 0.3),
        shape: RoundedRectangleBorder(
          borderRadius: IndustrialStyles.cardRadius,
          side: BorderSide(
            color: IndustrialTheme.processingPurple.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
        child: Container(
          padding: const EdgeInsets.all(IndustrialStyles.spacingL),
          decoration: BoxDecoration(
            borderRadius: IndustrialStyles.cardRadius,
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                IndustrialTheme.processingPurple.withValues(alpha: 0.05),
                IndustrialTheme.primaryBlue.withValues(alpha: 0.03),
              ],
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // 工业级进度指示器
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: IndustrialTheme.processingPurple.withValues(alpha: 0.2),
                    width: 2,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: IndustrialTheme.processingPurple.withValues(alpha: 0.1),
                      blurRadius: 8,
                      spreadRadius: 2,
                    ),
                  ],
                ),
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    SizedBox(
                      width: 60,
                      height: 60,
                      child: CircularProgressIndicator(
                        strokeWidth: 4,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          IndustrialTheme.processingPurple,
                        ),
                        backgroundColor: IndustrialTheme.processingPurple.withValues(alpha: 0.2),
                      ),
                    ),
                    Icon(
                      Icons.image_search,
                      color: IndustrialTheme.processingPurple,
                      size: IndustrialStyles.iconM,
                    ),
                  ],
                ),
              ),
              const SizedBox(width: IndustrialStyles.spacingL),
              // 状态信息
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.analytics,
                          color: IndustrialTheme.processingPurple,
                          size: IndustrialStyles.iconM,
                        ),
                        const SizedBox(width: IndustrialStyles.spacingS),
                        Text(
                          '图像解析处理中',
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            color: IndustrialTheme.processingPurple,
                            fontWeight: FontWeight.w600,
                            letterSpacing: 0.5,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: IndustrialStyles.spacingS),
                    Text(
                      '正在进行OCR识别和数据提取，请稍候...',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: IndustrialTheme.textSecondary,
                        letterSpacing: 0.25,
                      ),
                    ),
                    const SizedBox(height: IndustrialStyles.spacingS),
                    // 处理步骤指示
                    Row(
                      children: [
                        _buildProcessStep('图像预处理', true),
                        const SizedBox(width: IndustrialStyles.spacingS),
                        _buildProcessStep('文字识别', true),
                        const SizedBox(width: IndustrialStyles.spacingS),
                        _buildProcessStep('数据提取', false),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建处理步骤指示器
  Widget _buildProcessStep(String label, bool isActive) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: IndustrialStyles.spacingS,
        vertical: IndustrialStyles.spacingXS,
      ),
      decoration: BoxDecoration(
        color: isActive
            ? IndustrialTheme.processingPurple.withValues(alpha: 0.1)
            : IndustrialTheme.borderGrey.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isActive
              ? IndustrialTheme.processingPurple.withValues(alpha: 0.3)
              : IndustrialTheme.borderGrey,
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 6,
            height: 6,
            decoration: BoxDecoration(
              color: isActive
                  ? IndustrialTheme.processingPurple
                  : IndustrialTheme.textDisabled,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: isActive
                  ? IndustrialTheme.processingPurple
                  : IndustrialTheme.textDisabled,
              letterSpacing: 0.25,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // 1024x600宽屏，横向布局更合适
    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.projectName,
          style: TextStyle(fontSize: 22, fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.blue[700],
      ),
      body: _isObserving
          ? Stack(
              children: [
                if (_cameraController != null &&
                    _cameraController!.value.isInitialized)
                  // AspectRatio(
                  //   aspectRatio: _cameraController!.value.aspectRatio,
                  //   child: CameraPreview(_cameraController!),
                  // )
                  SizedBox.expand(
                    child: FittedBox(
                      fit: BoxFit.cover, // 让相机预览铺满且裁剪超出部分
                      child: SizedBox(
                        width: _cameraController!.value.previewSize!.height,
                        height: _cameraController!.value.previewSize!.width,
                        child: CameraPreview(_cameraController!),
                      ),
                    ),
                  )
                else
                  Center(child: CircularProgressIndicator()),
                Positioned(
                  top: 16,
                  right: 16,
                  child: FloatingActionButton(
                    mini: true,
                    backgroundColor: Colors.redAccent,
                    onPressed: () {
                      setState(() {
                        _isObserving = false;
                      });
                    },
                    child: Icon(Icons.close),
                  ),
                ),
              ],
            )
          : Padding(
              padding: const EdgeInsets.all(12.0),
              child: Column(
                children: [
                  // 主体内容区域
                  SizedBox(height: 12),
                  Expanded(
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // 左侧图片及识别信息
                        Expanded(
                          flex: 2,
                          child: SingleChildScrollView(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  '    仪表数据解析',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                SizedBox(height: 12),
                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    // 左侧原始图片 - 扩大显示区域
                                    Expanded(
                                      flex: 5,
                                      child: buildImageWithLabel(
                                        '原始图片',
                                        originalImage,
                                        'null',
                                        400,
                                        280,
                                        originalNumberController,
                                      ),
                                    ),
                                    SizedBox(width: 20),
                                    // 右侧裁剪图片 - 增大显示区域
                                    Expanded(
                                      flex: 3,
                                      child: Column(
                                        children: [
                                          buildImageWithLabel(
                                            '区域1识别结果',
                                            croppedImage1,
                                            croppedImage1Number,
                                            280,
                                            100,
                                            croppedNumber1Controller,
                                          ),
                                          SizedBox(height: 16),
                                          buildImageWithLabel(
                                            '区域2识别结果',
                                            croppedImage2,
                                            croppedImage2Number,
                                            280,
                                            100,
                                            croppedNumber2Controller,
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                                // IndustrialInfoPanel(
                                //   title: '操作提示',
                                //   icon: Icons.warning_amber_outlined,
                                //   headerColor: IndustrialTheme.getModuleColor(
                                //     'data',
                                //   ),
                                //   children: [
                                //     Text('请你务必将检测仪表放置在合适的位置。')
                                //   ],
                                // ),
                              ],
                            ),
                          ),
                        ),
                        SizedBox(width: 24),
                        // 右侧GPS信息
                        Expanded(
                          flex: 1,
                          child: SingleChildScrollView(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                IndustrialInfoPanel(
                                  title: '北斗定位信息',
                                  icon: Icons.gps_fixed,
                                  headerColor: IndustrialTheme.getModuleColor(
                                    'gps',
                                  ),
                                  children: [
                                    // IndustrialStatusIndicator(
                                    //   status: gpsStatus,
                                    //   label: gpsStatus,
                                    //   icon: Icons.location_on,
                                    // ),
                                    // const SizedBox(
                                    //   height: IndustrialStyles.spacingS,
                                    // ),
                                    IndustrialDataRow(
                                      label: '卫星',
                                      value: satelliteCount != null
                                          ? '$satelliteCount 颗'
                                          : '-',
                                      icon: Icons.satellite_alt,
                                      valueColor: satelliteCount != null && satelliteCount! >= 4
                                          ? IndustrialTheme.successGreen
                                          : IndustrialTheme.warningAmber,
                                    ),
                                    Divider(height: 1, thickness: 1),
                                    SizedBox(height: 16),
                                    IndustrialDataRow(
                                      label: '经度',
                                      value: latitude != null
                                          ? latitude!.toStringAsFixed(6)
                                          : '-',
                                      icon: Icons.my_location,
                                    ),
                                    IndustrialDataRow(
                                      label: '纬度',
                                      value: longitude != null
                                          ? longitude!.toStringAsFixed(6)
                                          : '-',
                                      icon: Icons.my_location,
                                    ),
                                    IndustrialDataRow(
                                      label: '高度(m)',
                                      value: altitude != null
                                          ? altitude!.toStringAsFixed(2)
                                          : '-',
                                      icon: Icons.height,
                                    ),
                                    Divider(height: 1, thickness: 1),
                                    SizedBox(height: 16),
                                    IndustrialDataRow(
                                      label: '精度(m)',
                                      value: accuracy != null
                                          ? accuracy!.toStringAsFixed(2)
                                          : '-',
                                      icon: Icons.gps_not_fixed,
                                      valueColor:
                                          accuracy != null && accuracy! <= 5
                                          ? IndustrialTheme.successGreen
                                          : IndustrialTheme.warningAmber,
                                    ),
                                    IndustrialDataRow(
                                      label: '点距(m)',
                                      value: collectionPointDistance >= 0
                                          ? '${collectionPointDistance.toStringAsFixed(2)}m'
                                          : '-',
                                      icon: Icons.straighten,
                                      valueColor: collectionPointDistance >= 0
                                          ? IndustrialTheme.successGreen
                                          : IndustrialTheme.textSecondary,
                                    ),
                                  ],
                                ),
                                // const SizedBox(
                                //   height: IndustrialStyles.spacingM,
                                // ),
                                
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 16),
                  if (_isProcessing)
                    _buildProcessingOverlay(),
                  Divider(height: 24, thickness: 1),
                  // 底部按钮
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      ElevatedButton(
                        // onPressed: () async {
                        //   // 1. 实时显示
                        //   final XFile? photo = await _picker.pickImage(
                        //     source: ImageSource.camera,
                        //   );
                        //   if (photo == null) {
                        //     ScaffoldMessenger.of(context).showSnackBar(
                        //       SnackBar(content: Text('退出设备实时观测状态')),
                        //     );
                        //     return;
                        //   }
                        //   File photoFile = File(photo.path);
                        // },
                        onPressed: () async {
                          setState(() {
                            _isObserving = true;
                          });
                        },
                        style: ElevatedButton.styleFrom(
                          minimumSize: Size(140, 48),
                          backgroundColor: Colors.green,
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.health_and_safety_outlined,
                              color: Colors.white,
                            ),
                            SizedBox(width: 8),
                            Text(
                              '设备观测',
                              style: TextStyle(
                                fontSize: 16,
                                color: Colors.white,
                              ),
                            ),
                          ],
                        ),
                      ),
                      ElevatedButton(
                        onPressed: collectData,
                        style: ElevatedButton.styleFrom(
                          minimumSize: Size(140, 48),
                          backgroundColor: Colors.blueAccent,
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.camera_alt, color: Colors.white),
                            SizedBox(width: 8),
                            Text(
                              '数据采集',
                              style: TextStyle(
                                fontSize: 16,
                                color: Colors.white,
                              ),
                            ),
                          ],
                        ),
                      ),
                      ElevatedButton(
                        onPressed: saveData,
                        style: ElevatedButton.styleFrom(
                          minimumSize: Size(140, 48),
                          backgroundColor: Colors.blue[800],
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.save, color: Colors.white),
                            SizedBox(width: 8),
                            Text(
                              '数据存储',
                              style: TextStyle(
                                fontSize: 16,
                                color: Colors.white,
                              ),
                            ),
                          ],
                        ),
                      ),
                      ElevatedButton(
                        onPressed: () {
                          // 项目内的采集点数据处理功能 - 可以添加具体实现
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => DataProcessingPage(
                                projectUniqueId: _storage.read(
                                  'currentProjectUniqueId',
                                ),
                                projectName: _storage.read(
                                  'currentProjectName',
                                ),
                              ),
                            ),
                          );
                        },
                        style: ElevatedButton.styleFrom(
                          minimumSize: Size(140, 48),
                          backgroundColor: Colors.blue[600],
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.save, color: Colors.white),
                            SizedBox(width: 8),
                            Text(
                              '数据处理',
                              style: TextStyle(
                                fontSize: 16,
                                color: Colors.white,
                              ),
                            ),
                          ],
                        ),
                      ),

                      ElevatedButton(
                        onPressed: () {
                          Navigator.pop(context);
                        },
                        style: ElevatedButton.styleFrom(
                          minimumSize: Size(140, 48),
                          backgroundColor: Colors.blueGrey,
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.home, color: Colors.white),
                            SizedBox(width: 8),
                            Text(
                              '返回主页',
                              style: TextStyle(
                                fontSize: 16,
                                color: Colors.white,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
    );
  }
}
