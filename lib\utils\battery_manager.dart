import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

/// 电池状态枚举
enum BatteryState {
  unknown,
  charging,
  discharging,
  notCharging,
  full,
}

/// 电池信息类
class BatteryInfo {
  final int level; // 电量百分比 (0-100)
  final BatteryState state; // 充电状态
  final bool isLowBattery; // 是否低电量
  final DateTime lastUpdated; // 最后更新时间

  BatteryInfo({
    required this.level,
    required this.state,
    required this.isLowBattery,
    required this.lastUpdated,
  });

  /// 获取状态显示文本
  String get stateText {
    switch (state) {
      case BatteryState.charging:
        return '充电中';
      case BatteryState.discharging:
        return '放电中';
      case BatteryState.notCharging:
        return '未充电';
      case BatteryState.full:
        return '已充满';
      case BatteryState.unknown:
      default:
        return '未知';
    }
  }

  /// 获取状态图标
  String get stateIcon {
    switch (state) {
      case BatteryState.charging:
        return '⚡';
      case BatteryState.full:
        return '🔋';
      case BatteryState.discharging:
        return level < 20 ? '🪫' : '🔋';
      case BatteryState.notCharging:
        return '🔋';
      case BatteryState.unknown:
      default:
        return '❓';
    }
  }
}

/// 电池管理器
/// 负责监控设备电池状态和电量
class BatteryManager extends ChangeNotifier {
  static final BatteryManager _instance = BatteryManager._internal();
  factory BatteryManager() => _instance;
  BatteryManager._internal();

  static BatteryManager get instance => _instance;

  static const MethodChannel _channel = MethodChannel('com.example.a325_lya_1000/battery');
  
  BatteryInfo? _batteryInfo;
  Timer? _updateTimer;
  bool _isMonitoring = false;

  /// 获取当前电池信息
  BatteryInfo? get batteryInfo => _batteryInfo;

  /// 是否正在监控
  bool get isMonitoring => _isMonitoring;

  /// 初始化电池管理器
  Future<void> initialize() async {
    try {
      await _updateBatteryInfo();
    } catch (e) {
      debugPrint('电池管理器初始化失败: $e');
    }
  }

  /// 开始监控电池状态
  void startMonitoring() {
    if (_isMonitoring) return;

    _isMonitoring = true;
    debugPrint('[电池管理器] 开始监控电池状态');

    // 立即更新一次
    _updateBatteryInfo();

    // 每30秒更新一次电池状态
    _updateTimer = Timer.periodic(const Duration(seconds: 30), (_) {
      _updateBatteryInfo();
    });
  }

  /// 停止监控电池状态
  void stopMonitoring() {
    if (!_isMonitoring) return;

    _isMonitoring = false;
    debugPrint('[电池管理器] 停止监控电池状态');

    _updateTimer?.cancel();
    _updateTimer = null;
  }

  /// 更新电池信息
  Future<void> _updateBatteryInfo() async {
    try {
      final result = await _channel.invokeMethod('getBatteryInfo');
      
      if (result != null && result is Map) {
        final level = result['level'] as int? ?? 0;
        final stateString = result['state'] as String? ?? 'unknown';
        
        final state = _parseState(stateString);
        final isLowBattery = level <= 20;

        _batteryInfo = BatteryInfo(
          level: level,
          state: state,
          isLowBattery: isLowBattery,
          lastUpdated: DateTime.now(),
        );

        notifyListeners();
        debugPrint('[电池管理器] 电池状态更新: ${level}% ${state.name}');
      }
    } catch (e) {
      debugPrint('[电池管理器] 获取电池信息失败: $e');
      
      // 如果获取失败，创建一个默认的电池信息
      _batteryInfo = BatteryInfo(
        level: 0,
        state: BatteryState.unknown,
        isLowBattery: true,
        lastUpdated: DateTime.now(),
      );
      notifyListeners();
    }
  }

  /// 解析电池状态字符串
  BatteryState _parseState(String stateString) {
    switch (stateString.toLowerCase()) {
      case 'charging':
        return BatteryState.charging;
      case 'discharging':
        return BatteryState.discharging;
      case 'not_charging':
        return BatteryState.notCharging;
      case 'full':
        return BatteryState.full;
      default:
        return BatteryState.unknown;
    }
  }

  /// 手动刷新电池状态
  Future<void> refresh() async {
    await _updateBatteryInfo();
  }

  @override
  void dispose() {
    stopMonitoring();
    super.dispose();
  }
}
