@echo off
echo ========================================
echo Android 13 Kiosk模式专用设置脚本
echo ========================================
echo.

REM 检查ADB连接
echo 1. 检查设备连接和Android版本...
adb devices
if %ERRORLEVEL% neq 0 (
    echo 错误: ADB未找到或设备未连接
    pause
    exit /b 1
)

REM 检查Android版本
for /f "tokens=*" %%i in ('adb shell getprop ro.build.version.release') do set ANDROID_VERSION=%%i
echo 检测到Android版本: %ANDROID_VERSION%

if not "%ANDROID_VERSION:~0,2%"=="13" (
    echo 警告: 此脚本专为Android 13设计，当前版本为 %ANDROID_VERSION%
    set /p continue="是否继续? (y/n): "
    if /i not "%continue%"=="y" exit /b 0
)

echo.
echo 2. 安装应用...
adb install -r build\app\outputs\flutter-apk\app-debug.apk
if %ERRORLEVEL% neq 0 (
    echo 错误: 应用安装失败
    pause
    exit /b 1
)

echo.
echo 3. 设置设备所有者（Android 13必需）...
echo 正在设置设备所有者...
adb shell dpm set-device-owner com.example.a325_lya_1000/.DeviceAdminReceiver
if %ERRORLEVEL% equ 0 (
    echo ✅ 设备所有者设置成功！
) else (
    echo ❌ 设备所有者设置失败
    echo Android 13需要设备所有者权限才能避免系统提示
    echo 请确保设备已恢复出厂设置且无用户账户
    pause
    exit /b 1
)

echo.
echo 4. 配置Android 13特定设置...

REM 设置锁定任务包
echo 配置锁定任务包...
adb shell dpm set-lock-task-packages com.example.a325_lya_1000/.DeviceAdminReceiver com.example.a325_lya_1000

REM 禁用系统导航栏
echo 禁用系统导航栏...
adb shell settings put global policy_control immersive.full=com.example.a325_lya_1000

REM 设置用户限制
echo 设置用户限制...
adb shell dpm add-user-restriction com.example.a325_lya_1000/.DeviceAdminReceiver no_safe_boot
adb shell dpm add-user-restriction com.example.a325_lya_1000/.DeviceAdminReceiver no_debugging_features

REM 禁用状态栏下拉
echo 禁用状态栏下拉...
adb shell dpm set-status-bar-disabled com.example.a325_lya_1000/.DeviceAdminReceiver true

echo.
echo 5. 验证设置...
echo 检查设备所有者状态:
adb shell dpm list-owners | findstr com.example.a325_lya_1000
if %ERRORLEVEL% equ 0 (
    echo ✅ 设备所有者已设置
) else (
    echo ❌ 设备所有者验证失败
)

echo.
echo 检查锁定任务包:
adb shell dpm get-lock-task-packages com.example.a325_lya_1000/.DeviceAdminReceiver

echo.
echo 6. 启动应用...
adb shell am start -n com.example.a325_lya_1000/.MainActivity
if %ERRORLEVEL% equ 0 (
    echo ✅ 应用启动成功
) else (
    echo ❌ 应用启动失败
)

echo.
echo ========================================
echo Android 13 Kiosk设置完成！
echo ========================================
echo.
echo 特殊说明:
echo - Android 13的安全策略更严格
echo - 必须设置为设备所有者才能避免系统提示
echo - 已配置专用的锁定任务包和用户限制
echo.
echo 管理员信息:
echo - 退出手势: 连续点击屏幕7次
echo - 管理员密码: LYA1000ADMIN
echo.
echo 如果仍然看到系统提示:
echo 1. 确认设备所有者设置成功
echo 2. 重启设备后再次测试
echo 3. 检查是否有其他安全软件干扰
echo.
pause
