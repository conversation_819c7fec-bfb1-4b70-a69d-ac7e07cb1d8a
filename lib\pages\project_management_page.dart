import 'package:flutter/material.dart';
import 'package:shirne_dialog/shirne_dialog.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../service/openai_client.dart';
import 'AiCreateNewProjectDialog.dart'; // 确保导入路径正确
import 'package:get_storage/get_storage.dart';
import 'package:uuid/uuid.dart';
import 'package:syncfusion_flutter_xlsio/xlsio.dart' as xlsio;
import 'package:path_provider/path_provider.dart';
import 'dart:io';

import 'data_processing_page.dart'; // 新增导入

class Project {
  final int? id;
  final String uniqueId; // 新增唯一ID字段
  final String name;
  final String location;
  final String date;
  final String signalEntryPoint;
  final String startPoint;
  final String diameter;
  final String thickness;
  final String material;
  final String medium;
  final String coating;
  final String soilResistivity;
  final String workingFrequency;
  final String initialCurrent;
  final String detectionDirection;

  Project({
    this.id,
    required this.uniqueId, // 构造函数新增
    required this.name,
    required this.location,
    required this.date,
    required this.signalEntryPoint,
    required this.startPoint,
    required this.diameter,
    required this.thickness,
    required this.material,
    required this.medium,
    required this.coating,
    required this.soilResistivity,
    required this.workingFrequency,
    required this.initialCurrent,
    required this.detectionDirection,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'uniqueId': uniqueId, // 新增
      'name': name,
      'location': location,
      'date': date,
      'signalEntryPoint': signalEntryPoint,
      'startPoint': startPoint,
      'diameter': diameter,
      'thickness': thickness,
      'material': material,
      'medium': medium,
      'coating': coating,
      'soilResistivity': soilResistivity,
      'workingFrequency': workingFrequency,
      'initialCurrent': initialCurrent,
      'detectionDirection': detectionDirection,
    };
  }
}

class ProjectManagementPage extends StatefulWidget {
  @override
  ProjectManagementPageState createState() => ProjectManagementPageState();
}

class ProjectManagementPageState extends State<ProjectManagementPage> {
  List<Project> projects = [];
  Project? selectedProject;
  final uuid = Uuid(); // 新增UUID生成器

  // 控制器
  TextEditingController nameController = TextEditingController();
  TextEditingController locationController = TextEditingController();
  TextEditingController dateController = TextEditingController();
  TextEditingController signalEntryPointController = TextEditingController();
  TextEditingController startPointController = TextEditingController();
  TextEditingController diameterController = TextEditingController();
  TextEditingController thicknessController = TextEditingController();
  TextEditingController materialController = TextEditingController();
  TextEditingController mediumController = TextEditingController();
  TextEditingController coatingController = TextEditingController();
  TextEditingController soilResistivityController = TextEditingController();
  TextEditingController workingFrequencyController = TextEditingController();
  TextEditingController initialCurrentController = TextEditingController();
  TextEditingController detectionDirectionController = TextEditingController();

  final service = OpenAIService(
    // baseURL: 'https://openrouter.ai/api/v1/chat/completions',
    baseURL: 'https://cup-ollama.ailer.ltd/v1/chat/completions',
    apiKey:
        '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',
  );

  final GetStorage _storage = GetStorage();

  // 导出Excel功能
  Future<void> exportToExcel(BuildContext context) async {
    if (selectedProject == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('请先选择一个项目')),
      );
      return;
    }

    try {
      // 创建Excel工作簿
      final xlsio.Workbook workbook = xlsio.Workbook();
      final xlsio.Worksheet worksheet = workbook.worksheets[0];

      // 设置工作表名称
      worksheet.name = selectedProject!.name;

      // 添加项目基本信息
      worksheet.getRangeByName('A1').setText('检测地点');
      worksheet.getRangeByName('B1').setText(selectedProject!.location);
      worksheet.getRangeByName('A2').setText('检测日期');
      worksheet.getRangeByName('B2').setText(selectedProject!.date);
      worksheet.getRangeByName('A3').setText('管径');
      worksheet.getRangeByName('B3').setText(selectedProject!.diameter);
      worksheet.getRangeByName('A4').setText('壁厚');
      worksheet.getRangeByName('B4').setText(selectedProject!.thickness);
      worksheet.getRangeByName('A5').setText('防腐层');
      worksheet.getRangeByName('B5').setText(selectedProject!.coating);
      worksheet.getRangeByName('A6').setText('工作频率');
      worksheet.getRangeByName('B6').setText(selectedProject!.workingFrequency);
      worksheet.getRangeByName('A7').setText('项目名称');
      worksheet.getRangeByName('B7').setText(selectedProject!.name);

      // 添加检测数据表头
      worksheet.getRangeByName('A9').setText('序号');
      worksheet.getRangeByName('B9').setText('距离');
      worksheet.getRangeByName('C9').setText('里程');
      worksheet.getRangeByName('D9').setText('电流mA');
      worksheet.getRangeByName('E9').setText('埋深');
      worksheet.getRangeByName('F9').setText('描述');
      worksheet.getRangeByName('G9').setText('北纬');
      worksheet.getRangeByName('H9').setText('东经');
      worksheet.getRangeByName('I9').setText('高度');
      worksheet.getRangeByName('J9').setText('东经');
      worksheet.getRangeByName('K9').setText('东经');
      worksheet.getRangeByName('L9').setText('仪表图片');

      // 获取数据收集数据
      final Database db = await openDatabase(
        join(await getDatabasesPath(), 'data_processing.db'),
      );

      final tableName = selectedProject!.uniqueId;
      final List<Map<String, dynamic>> dataRows = await db.query('`$tableName`', orderBy: 'id ASC');

      // 添加数据行
      for (int i = 0; i < dataRows.length; i++) {
        final row = dataRows[i];
        final rowIndex = i + 10; // 从第10行开始

        worksheet.getRangeByName('A$rowIndex').setNumber((i + 1).toDouble());
        worksheet.getRangeByName('B$rowIndex').setNumber(row['distance'] ?? 0);
        worksheet.getRangeByName('C$rowIndex').setText(row['mileage'] ?? '');
        worksheet.getRangeByName('D$rowIndex').setNumber(row['current'] ?? 0);
        worksheet.getRangeByName('E$rowIndex').setNumber(row['depth'] ?? 0);
        worksheet.getRangeByName('F$rowIndex').setText(row['description'] ?? '');
        worksheet.getRangeByName('G$rowIndex').setNumber(row['latitude'] ?? 0);
        worksheet.getRangeByName('H$rowIndex').setNumber(row['longitude'] ?? 0);
        worksheet.getRangeByName('I$rowIndex').setNumber(row['altitude'] ?? 0);
        worksheet.getRangeByName('J$rowIndex').setNumber(row['longitude'] ?? 0);
        worksheet.getRangeByName('K$rowIndex').setNumber(row['longitude'] ?? 0);

        // 添加图片到L列
        final imagePath = row['original_image_path'] as String?;
        if (imagePath != null && imagePath.isNotEmpty) {
          try {
            final imageFile = File(imagePath);
            if (await imageFile.exists()) {
              final imageBytes = await imageFile.readAsBytes();
              final picture = worksheet.pictures.addStream(rowIndex, 12, imageBytes);
              // 设置图片大小和位置
              picture.height = 80;
              picture.width = 120;
              // 调整行高以适应图片
              worksheet.setRowHeightInPixels(rowIndex, 80);
            }
          } catch (e) {
            // 如果图片加载失败，在单元格中显示"图片加载失败"
            worksheet.getRangeByName('L$rowIndex').setText('图片加载失败');
          }
        } else {
          // 如果没有图片路径，显示"无图片"
          worksheet.getRangeByName('L$rowIndex').setText('无图片');
        }
      }

      await db.close();

      // 保存Excel文件
      final List<int> bytes = workbook.saveAsStream();
      workbook.dispose();

      // 获取下载目录
      final directory = await getApplicationDocumentsDirectory();
      final downloadDir = Directory('${directory.path}/Download');
      if (!await downloadDir.exists()) {
        await downloadDir.create(recursive: true);
      }

      final fileName = '${selectedProject!.name}_${DateTime.now().millisecondsSinceEpoch}.xlsx';
      final file = File('${downloadDir.path}/$fileName');
      await file.writeAsBytes(bytes);

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Excel文件已导出到: ${file.path}')),
        );
      }

    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('导出失败: $e')),
        );
      }
    }
  }

  @override
  void initState() {
    super.initState();

    // 填充默认值
    nameController.text = "32701部队管道检测";
    locationController.text = "房山汽油管道";
    dateController.text = "2.25";
    signalEntryPointController.text = "测试桩";
    startPointController.text = "测试桩";
    diameterController.text = "219mm";
    thicknessController.text = "7mm";
    materialController.text = "20号钢";
    mediumController.text = "汽油";
    coatingController.text = "3LPE";
    soilResistivityController.text = "50";
    workingFrequencyController.text = "128";
    initialCurrentController.text = "1000";
    detectionDirectionController.text = "逆流";

    loadProjects();
  }

  Future<void> loadProjects() async {
    final Database db = await openDatabase(
      join(await getDatabasesPath(), 'projects_database_0707.db'),
      onCreate: (db, version) {
        return db.execute('''
          CREATE TABLE projects(
            id INTEGER PRIMARY KEY, 
            uniqueId TEXT UNIQUE,
            name TEXT, 
            location TEXT, 
            date TEXT,
            signalEntryPoint TEXT,
            startPoint TEXT,
            diameter TEXT,
            thickness TEXT,
            material TEXT,
            medium TEXT,
            coating TEXT,
            soilResistivity TEXT,
            workingFrequency TEXT,
            initialCurrent TEXT,
            detectionDirection TEXT
          )
          ''');
      },
      version: 1,
    );

    final List<Map<String, dynamic>> maps = await db.query('projects');
    setState(() {
      projects = List.generate(maps.length, (i) {
        return Project(
          id: maps[i]['id'],
          uniqueId: maps[i]['uniqueId'], // 新增读取
          name: maps[i]['name'],
          location: maps[i]['location'],
          date: maps[i]['date'],
          signalEntryPoint: maps[i]['signalEntryPoint'] ?? '',
          startPoint: maps[i]['startPoint'] ?? '',
          diameter: maps[i]['diameter'] ?? '',
          thickness: maps[i]['thickness'] ?? '',
          material: maps[i]['material'] ?? '',
          medium: maps[i]['medium'] ?? '',
          coating: maps[i]['coating'] ?? '',
          soilResistivity: maps[i]['soilResistivity'] ?? '',
          workingFrequency: maps[i]['workingFrequency'] ?? '',
          initialCurrent: maps[i]['initialCurrent'] ?? '',
          detectionDirection: maps[i]['detectionDirection'] ?? '',
        );
      });
    });
  }

  Future<void> saveProject() async {
    if (selectedProject == null) {
      // Create new project
      final newProject = Project(
        uniqueId: uuid.v4(), // 生成唯一ID
        name: nameController.text,
        location: locationController.text,
        date: dateController.text,
        signalEntryPoint: signalEntryPointController.text,
        startPoint: startPointController.text,
        diameter: diameterController.text,
        thickness: thicknessController.text,
        material: materialController.text,
        medium: mediumController.text,
        coating: coatingController.text,
        soilResistivity: soilResistivityController.text,
        workingFrequency: workingFrequencyController.text,
        initialCurrent: initialCurrentController.text,
        detectionDirection: detectionDirectionController.text,
      );

      final Database db = await openDatabase(
        join(await getDatabasesPath(), 'projects_database_0707.db'),
        version: 1,
      );

      await db.insert(
        'projects',
        newProject.toMap(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    } else {
      // Update existing project
      final updatedProject = Project(
        id: selectedProject!.id,
        uniqueId: selectedProject!.uniqueId, // 保留旧ID
        name: nameController.text,
        location: locationController.text,
        date: dateController.text,
        signalEntryPoint: signalEntryPointController.text,
        startPoint: startPointController.text,
        diameter: diameterController.text,
        thickness: thicknessController.text,
        material: materialController.text,
        medium: mediumController.text,
        coating: coatingController.text,
        soilResistivity: soilResistivityController.text,
        workingFrequency: workingFrequencyController.text,
        initialCurrent: initialCurrentController.text,
        detectionDirection: detectionDirectionController.text,
      );

      final Database db = await openDatabase(
        join(await getDatabasesPath(), 'projects_database_0707.db'),
        version: 1,
      );

      await db.update(
        'projects',
        updatedProject.toMap(),
        where: 'id = ?',
        whereArgs: [updatedProject.id],
      );
    }

    loadProjects();
    clearForm();
  }

  Future<void> deleteProject() async {
    if (selectedProject != null) {
      final Database db = await openDatabase(
        join(await getDatabasesPath(), 'projects_database_0707.db'),
        version: 1,
      );

      await db.delete(
        'projects',
        where: 'id = ?',
        whereArgs: [selectedProject!.id],
      );

      loadProjects();
      clearForm();
    }
  }

  void clearForm() {
    setState(() {
      selectedProject = null;
      nameController.text = "32701部队管道检测";
      locationController.text = "房山汽油管道";
      dateController.text = "2.25";
      signalEntryPointController.text = "测试桩";
      startPointController.text = "测试桩";
      diameterController.text = "219mm";
      thicknessController.text = "7mm";
      materialController.text = "20号钢";
      mediumController.text = "汽油";
      coatingController.text = "3LPE";
      soilResistivityController.text = "50";
      workingFrequencyController.text = "128";
      initialCurrentController.text = "1000";
      detectionDirectionController.text = "逆流";
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('项目管理'), backgroundColor: Colors.blue[700]),
      resizeToAvoidBottomInset: true,
      body: Row(
        children: [
          // Left side: Project list
          Expanded(
            flex: 1,
            child: Container(
              decoration: BoxDecoration(
                color: Colors.grey[200],
                border: Border(right: BorderSide(color: Colors.grey[400]!)),
              ),
              child: ListView.separated(
                itemCount: projects.length + 1,
                separatorBuilder: (context, index) => Divider(height: 1),
                itemBuilder: (context, index) {
                  if (index == projects.length) {
                    return ListTile(
                      leading: Icon(Icons.add_circle, color: Colors.green),
                      title: Text(
                        '新建项目',
                        style: TextStyle(color: Colors.green),
                      ),
                      onTap: () {
                        clearForm();
                      },
                    );
                  }
                  return ListTile(
                    leading: Icon(Icons.folder, color: Colors.blue[700]),
                    title: Text(
                      projects[index].name,
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    ),
                    onTap: () {
                      setState(() {
                        selectedProject = projects[index];
                        nameController.text = selectedProject!.name;
                        locationController.text = selectedProject!.location;
                        dateController.text = selectedProject!.date;
                        signalEntryPointController.text =
                            selectedProject!.signalEntryPoint;
                        startPointController.text = selectedProject!.startPoint;
                        diameterController.text = selectedProject!.diameter;
                        thicknessController.text = selectedProject!.thickness;
                        materialController.text = selectedProject!.material;
                        mediumController.text = selectedProject!.medium;
                        coatingController.text = selectedProject!.coating;
                        soilResistivityController.text =
                            selectedProject!.soilResistivity;
                        workingFrequencyController.text =
                            selectedProject!.workingFrequency;
                        initialCurrentController.text =
                            selectedProject!.initialCurrent;
                        detectionDirectionController.text =
                            selectedProject!.detectionDirection;

                        // 每次点击的时候，更新本地存储的当前项目名称和ID
                        _storage.write(
                          'currentProjectName',
                          selectedProject!.name,
                        );
                        _storage.write(
                          'currentProjectId',
                          selectedProject!.id,
                        );
                        _storage.write(
                          'currentProjectUniqueId',
                          selectedProject!.uniqueId,
                        );
                        print('项目ID：${selectedProject!.id}');
                        print('项目uniqueID：${selectedProject!.uniqueId}');
                      });
                    },
                  );
                },
              ),
            ),
          ),
          // Right side: Project details
          Expanded(
            flex: 2,
            child: Container(
              padding: const EdgeInsets.all(16.0),
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.5),
                    spreadRadius: 1,
                    blurRadius: 5,
                    offset: Offset(0, 3),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [

                      Expanded(
                        child: Text(
                          '项目详情-' +
                              (_storage.read('currentProjectName') ??
                                  '请在左侧列表选择项目'),
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      ElevatedButton.icon(
                        onPressed: () async {
                          await showDialog(
                            context: context,
                            builder: (BuildContext context) {
                              return ProjectDialog(service: service);
                            },
                          );

                          await loadProjects();
                        },
                        icon: Icon(Icons.mic_none_rounded, color: Colors.white),
                        label: Text(
                          '智能录入',
                          style: TextStyle(color: Colors.white),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.orangeAccent,
                          padding: EdgeInsets.symmetric(
                            horizontal: 20,
                            vertical: 12,
                          ),
                        ),
                      ),
                      SizedBox(width: 10),
                      ElevatedButton.icon(
                        onPressed: () async {
                          await exportToExcel(context);
                        },
                        icon: Icon(Icons.explicit_outlined, color: Colors.white),
                        label: Text(
                          '导出EXCEL',
                          style: TextStyle(color: Colors.white),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.lightBlue,
                          padding: EdgeInsets.symmetric(
                            horizontal: 20,
                            vertical: 12,
                          ),
                        ),
                      ),
                    ],
                  ),

                  Divider(height: 20, thickness: 1),
                  SizedBox(height: 6),
                  Expanded(
                    child: ListView(
                      children: [
                        // 基本信息
                        Center(
                          child: Text(
                            "基本信息",
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.blueAccent, // 设置字体颜色为蓝色
                            ),
                          ),
                        ),
                        SizedBox(height: 8),
                        TextField(
                          controller: nameController,
                          decoration: InputDecoration(
                            labelText: '项目名称',
                            prefixIcon: Icon(Icons.title),
                            border: OutlineInputBorder(),
                          ),
                        ),
                        SizedBox(height: 10),
                        TextField(
                          controller: locationController,
                          decoration: InputDecoration(
                            labelText: '检测地点',
                            prefixIcon: Icon(Icons.location_on),
                            border: OutlineInputBorder(),
                          ),
                        ),
                        SizedBox(height: 10),
                        TextField(
                          controller: dateController,
                          decoration: InputDecoration(
                            labelText: '检测日期',
                            prefixIcon: Icon(Icons.calendar_today),
                            border: OutlineInputBorder(),
                          ),
                        ),

                        SizedBox(height: 20),
                        // 检测点信息
                        Center(
                          child: Text(
                            "监测点信息",
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.blueAccent, // 设置字体颜色为蓝色
                            ),
                          ),
                        ),
                        SizedBox(height: 8),
                        TextField(
                          controller: signalEntryPointController,
                          decoration: InputDecoration(
                            labelText: '信号供入点',
                            prefixIcon: Icon(Icons.input),
                            border: OutlineInputBorder(),
                          ),
                        ),
                        SizedBox(height: 10),
                        TextField(
                          controller: startPointController,
                          decoration: InputDecoration(
                            labelText: '起测点位置',
                            prefixIcon: Icon(Icons.play_arrow),
                            border: OutlineInputBorder(),
                          ),
                        ),

                        SizedBox(height: 20),
                        // 管道信息
                        Center(
                          child: Text(
                            "管道信息",
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.blueAccent, // 设置字体颜色为蓝色
                            ),
                          ),
                        ),
                        SizedBox(height: 8),
                        TextField(
                          controller: diameterController,
                          decoration: InputDecoration(
                            labelText: '管径',
                            prefixIcon: Icon(Icons.radio_button_unchecked),
                            border: OutlineInputBorder(),
                          ),
                        ),
                        SizedBox(height: 10),
                        TextField(
                          controller: thicknessController,
                          decoration: InputDecoration(
                            labelText: '壁厚',
                            prefixIcon: Icon(Icons.line_weight),
                            border: OutlineInputBorder(),
                          ),
                        ),
                        SizedBox(height: 10),
                        TextField(
                          controller: materialController,
                          decoration: InputDecoration(
                            labelText: '管材',
                            prefixIcon: Icon(Icons.category),
                            border: OutlineInputBorder(),
                          ),
                        ),
                        SizedBox(height: 10),
                        TextField(
                          controller: mediumController,
                          decoration: InputDecoration(
                            labelText: '管内介质',
                            prefixIcon: Icon(Icons.waves),
                            border: OutlineInputBorder(),
                          ),
                        ),
                        SizedBox(height: 10),
                        TextField(
                          controller: coatingController,
                          decoration: InputDecoration(
                            labelText: '防腐层',
                            prefixIcon: Icon(Icons.shield),
                            border: OutlineInputBorder(),
                          ),
                        ),

                        SizedBox(height: 20),
                        // 检测参数
                        Center(
                          child: Text(
                            "检测参数",
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.blueAccent, // 设置字体颜色为蓝色
                            ),
                          ),
                        ),

                        SizedBox(height: 8),
                        TextField(
                          controller: soilResistivityController,
                          decoration: InputDecoration(
                            labelText: '土壤电阻率',
                            prefixIcon: Icon(Icons.terrain),
                            border: OutlineInputBorder(),
                          ),
                        ),
                        SizedBox(height: 10),
                        TextField(
                          controller: workingFrequencyController,
                          decoration: InputDecoration(
                            labelText: '工作频率',
                            prefixIcon: Icon(Icons.graphic_eq),
                            border: OutlineInputBorder(),
                          ),
                        ),
                        SizedBox(height: 10),
                        TextField(
                          controller: initialCurrentController,
                          decoration: InputDecoration(
                            labelText: '初始电流值',
                            prefixIcon: Icon(Icons.flash_on),
                            border: OutlineInputBorder(),
                          ),
                        ),
                        SizedBox(height: 10),
                        TextField(
                          controller: detectionDirectionController,
                          decoration: InputDecoration(
                            labelText: '检测方向',
                            prefixIcon: Icon(Icons.arrow_forward),
                            border: OutlineInputBorder(),
                          ),
                        ),
                      ],
                    ),
                  ),

                  // SizedBox(height: 24),
                  Divider(height: 16, thickness: 2),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [

                      ElevatedButton.icon(
                        onPressed: saveProject,
                        icon: Icon(Icons.save, color: Colors.white),
                        label: Text(
                          '保存项目',
                          style: TextStyle(color: Colors.white),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          padding: EdgeInsets.symmetric(
                            horizontal: 20,
                            vertical: 12,
                          ),
                        ),
                      ),
                      ElevatedButton.icon(
                        onPressed: () {
                          // 项目内的采集点数据处理功能 - 可以添加具体实现
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => DataProcessingPage(
                                projectUniqueId: _storage.read('currentProjectUniqueId'),
                                projectName: _storage.read('currentProjectName'),
                              ),
                            ),
                          );
                        },
                        icon: Icon(Icons.list_alt_sharp, color: Colors.white),
                        label: Text(
                          '数据处理',
                          style: TextStyle(color: Colors.white),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.lightBlue,
                          padding: EdgeInsets.symmetric(
                            horizontal: 20,
                            vertical: 12,
                          ),
                        ),
                      ),
                      ElevatedButton.icon(
                        onPressed: deleteProject,
                        icon: Icon(Icons.delete, color: Colors.white),
                        label: Text(
                          '删除项目',
                          style: TextStyle(color: Colors.white),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.red,
                          padding: EdgeInsets.symmetric(
                            horizontal: 20,
                            vertical: 12,
                          ),
                        ),
                      ),
                      ElevatedButton.icon(
                        onPressed: () {
                          Navigator.pop(context);
                        },
                        icon: Icon(Icons.home, color: Colors.white),
                        label: Text(
                          '返回主页',
                          style: TextStyle(color: Colors.white),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blueGrey,
                          padding: EdgeInsets.symmetric(
                            horizontal: 20,
                            vertical: 12,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
