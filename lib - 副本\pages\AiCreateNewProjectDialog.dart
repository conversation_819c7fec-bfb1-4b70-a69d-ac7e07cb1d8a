import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:sqflite/sqflite.dart';
import '../service/openai_client.dart';
import 'package:get_storage/get_storage.dart';
import 'dart:ui';
import 'project_management_page.dart';
import 'package:path/path.dart';
import 'package:uuid/uuid.dart'; // 新增导入

class ProjectDialog extends StatefulWidget {
  final OpenAIService service;

  const ProjectDialog({Key? key, required this.service}) : super(key: key);

  @override
  _ProjectDialogState createState() => _ProjectDialogState();
}

class _ProjectDialogState extends State<ProjectDialog>
    with SingleTickerProviderStateMixin {
  final TextEditingController _leftController = TextEditingController();
  final TextEditingController _rightController = TextEditingController();
  final GetStorage _storage = GetStorage();
  late AnimationController _animationController;
  late Animation<double> _animation;

  final service = OpenAIService(
    // baseURL: 'https://openrouter.ai/api/v1/chat/completions',
    baseURL: 'https://cup-ollama.ailer.ltd/v1/chat/completions',
    apiKey:
        '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',
  );

  bool _loading = false;
  bool _aiResponded = false;

  @override
  void initState() {
    super.initState();
    _leftController.text = _storage.read('projectDescForAi') ?? '';
    _rightController.text = _storage.read('projectDescByAi') ?? '';
    _aiResponded = _rightController.text.isNotEmpty;

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );

    if (_leftController.text.isNotEmpty && _leftController.text.length >= 10) {
      _callAI();
    }
  }

  Future<void> _callAI() async {
    final input = _leftController.text.trim();
    if (input.isEmpty) {
      _rightController.clear();
      _storage.remove('projectDescByAi');
      setState(() {
        _aiResponded = false;
      });
      return;
    }

    setState(() {
      _loading = true;
    });
    _animationController.forward();

//     final messages = [
//       {
//         'role': 'user',
//         'content':
//             '''请根据文段内容，按照如下所示的数据结构，提炼并完善??代表的待补充项。\n\n
// 1. 项目名称: ??
// 2. 检测地点: ??
// 3. 检测日期: ??
// 4. 信号供入点: ??
// 5. 起测点位置: ??
// 6. 管径: ??
// 7. 壁厚: ??
// 8. 管材: ??
// 9. 管内介质: ??
// 10. 防腐层: ??
// 11. 土壤电阻率: ??
// 12. 工作频率: ??
// 13. 初始电流值: ??
// 14. 检测方向: ??\n\n 对于无法提炼的信息，标记为：待补充。\n\n待提炼的信息：\n\n''' +
//             input,
//       },
//     ];

        final messages = [
      {
        'role': 'user',
        'content':
            '''请根据文段内容，按照如下所示的数据结构直接返回，提炼并完善??代表的待补充项。\n\n
{
      "name": "??",
    "location": "??",
    "date": "??",
    "signalEntryPoint": "??",
    "startPoint": "??",
    "diameter": "??",
    "thickness": "??",
    "material": "??",
    "medium": "??",
    "coating": "??",
    "soilResistivity": "??",
    "workingFrequency": "??",
    "initialCurrent": "??",
    "detectionDirection": "??"
  }
\n\n 对于无法提炼的信息，标记为：待补充。\n\n需要你提炼的信息如下：\n\n''' +
            input,
      },
    ];


    try {
      final response = await widget.service.chatCompletion(
        messages: messages,
        model: 'glm4:9b',
      );
      _rightController.text = response;
      _storage.write('projectDescByAi', response);
      setState(() {
        _aiResponded = true;
      });
    } catch (e) {
      _rightController.text = '调用AI失败: $e';
      _storage.remove('projectDescByAi');
      setState(() {
        _aiResponded = false;
      });
    } finally {
      setState(() {
        _loading = false;
      });
    }
  }

  @override
  void dispose() {
    _storage.write('projectDescForAi', _leftController.text.trim());
    _storage.write('projectDescByAi', _rightController.text.trim());
    _leftController.dispose();
    _rightController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return BackdropFilter(
      filter: ImageFilter.blur(sigmaX: 4, sigmaY: 4),
      child: Dialog(
        backgroundColor: Colors.transparent,
        elevation: 0,
        insetPadding: const EdgeInsets.symmetric(horizontal: 40, vertical: 24),
        child: Container(
          width: MediaQuery.of(context).size.width * 0.85,
          height: MediaQuery.of(context).size.height * 0.75,
          decoration: BoxDecoration(
            color: colorScheme.surface,
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.15),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
              // Header
              Container(
                padding: const EdgeInsets.fromLTRB(24, 20, 24, 16),
                decoration: BoxDecoration(
                  color: colorScheme.primary.withOpacity(0.08),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(20),
                    topRight: Radius.circular(20),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(Icons.add_chart, color: colorScheme.primary, size: 28),
                    const SizedBox(width: 16),
                    Text(
                      'AI 辅助创建新项目',
                      style: textTheme.titleLarge?.copyWith(
                        color: colorScheme.primary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      icon: const Icon(Icons.close),
                      onPressed: () => Navigator.of(context).pop(),
                      tooltip: '关闭',
                      style: IconButton.styleFrom(
                        foregroundColor: colorScheme.onSurface.withOpacity(0.7),
                      ),
                    ),
                  ],
                ),
              ),

              // Content
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(24),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Left panel
                      Expanded(
                        flex: 5,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.description_outlined,
                                  size: 20,
                                  color: colorScheme.primary,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  '项目描述',
                                  style: textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 12),
                            Expanded(
                              child: Container(
                                decoration: BoxDecoration(
                                  color: colorScheme.surface,
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(
                                    color: colorScheme.outline.withOpacity(0.5),
                                  ),
                                ),
                                child: TextField(
                                  controller: _leftController,
                                  maxLines: null,
                                  expands: true,
                                  style: textTheme.bodyLarge,
                                  decoration: InputDecoration(
                                    hintText:
                                        '请输入项目描述，例如：本项目为32701部队管道检测，于2月25日对房山汽油管道进行检测...',
                                    hintStyle: textTheme.bodyMedium?.copyWith(
                                      color: colorScheme.onSurface.withOpacity(
                                        0.5,
                                      ),
                                    ),
                                    contentPadding: const EdgeInsets.all(16),
                                    border: InputBorder.none,
                                  ),
                                  onChanged: (value) {
                                    _storage.write(
                                      'projectDescForAi',
                                      value.trim(),
                                    );
                                    if (value.trim().length >= 10) {
                                      _callAI();
                                    } else {
                                      _rightController.clear();
                                      _storage.remove('projectDescByAi');
                                      setState(() {
                                        _aiResponded = false;
                                      });
                                    }
                                  },
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),

                      // Middle divider with animation
                      AnimatedContainer(
                        duration: const Duration(milliseconds: 300),
                        width: 60,
                        child: Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              if (_loading)
                                Container(
                                  width: 36,
                                  height: 36,
                                  padding: const EdgeInsets.all(8),
                                  decoration: BoxDecoration(
                                    color: colorScheme.primaryContainer,
                                    shape: BoxShape.circle,
                                  ),
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2.5,
                                    color: colorScheme.primary,
                                  ),
                                )
                              else if (_aiResponded)
                                Container(
                                  width: 36,
                                  height: 36,
                                  decoration: BoxDecoration(
                                    color: colorScheme.primaryContainer,
                                    shape: BoxShape.circle,
                                  ),
                                  child: Icon(
                                    Icons.arrow_forward,
                                    color: colorScheme.primary,
                                  ),
                                )
                              else
                                Container(
                                  width: 36,
                                  height: 36,
                                  decoration: BoxDecoration(
                                    color: colorScheme.surfaceVariant,
                                    shape: BoxShape.circle,
                                  ),
                                  child: Icon(
                                    Icons.arrow_forward,
                                    color: colorScheme.onSurfaceVariant
                                        .withOpacity(0.5),
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ),

                      // Right panel
                      Expanded(
                        flex: 5,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.smart_toy_outlined,
                                  size: 20,
                                  color: _aiResponded
                                      ? colorScheme.primary
                                      : colorScheme.onSurface.withOpacity(0.5),
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  'AI 分析结果',
                                  style: textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.w600,
                                    color: _aiResponded
                                        ? colorScheme.onSurface
                                        : colorScheme.onSurface.withOpacity(
                                            0.5,
                                          ),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 12),
                            Expanded(
                              child: AnimatedContainer(
                                duration: const Duration(milliseconds: 300),
                                decoration: BoxDecoration(
                                  color: _aiResponded
                                      ? colorScheme.surface
                                      : colorScheme.surfaceVariant.withOpacity(
                                          0.3,
                                        ),
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(
                                    color: _aiResponded
                                        ? colorScheme.primary.withOpacity(0.5)
                                        : colorScheme.outline.withOpacity(0.3),
                                  ),
                                ),
                                child: Stack(
                                  children: [
                                    // Background pattern when empty
                                    if (!_aiResponded)
                                      Center(
                                        child: Column(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            Icon(
                                              Icons.smart_toy_outlined,
                                              size: 48,
                                              color: colorScheme
                                                  .onSurfaceVariant
                                                  .withOpacity(0.2),
                                            ),
                                            const SizedBox(height: 16),
                                            Text(
                                              '等待AI分析',
                                              style: textTheme.bodyLarge
                                                  ?.copyWith(
                                                    color: colorScheme
                                                        .onSurfaceVariant
                                                        .withOpacity(0.5),
                                                  ),
                                            ),
                                          ],
                                        ),
                                      ),

                                    // Actual content
                                    TextField(
                                      controller: _rightController,
                                      maxLines: null,
                                      expands: true,
                                      readOnly: true,
                                      style: textTheme.bodyLarge?.copyWith(
                                        color: _aiResponded
                                            ? colorScheme.onSurface
                                            : colorScheme.onSurface.withOpacity(
                                                0,
                                              ),
                                      ),
                                      decoration: InputDecoration(
                                        contentPadding: const EdgeInsets.all(
                                          16,
                                        ),
                                        border: InputBorder.none,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // Footer
              Container(
                padding: const EdgeInsets.fromLTRB(24, 16, 24, 24),
                decoration: BoxDecoration(
                  color: colorScheme.surface,
                  borderRadius: const BorderRadius.only(
                    bottomLeft: Radius.circular(20),
                    bottomRight: Radius.circular(20),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      blurRadius: 10,
                      offset: const Offset(0, -5),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    // Tips
                    Expanded(
                      child: Row(
                        children: [
                          Icon(
                            Icons.info_outline,
                            size: 16,
                            color: colorScheme.primary.withOpacity(0.7),
                          ),
                          const SizedBox(width: 8),
                          Flexible(
                            child: Text(
                              '提示: 输入详细的项目描述可获得更准确的AI分析结果',
                              style: textTheme.bodySmall?.copyWith(
                                color: colorScheme.onSurfaceVariant,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Action buttons
                    Row(
                      children: [
                        OutlinedButton(
                          onPressed: () => Navigator.of(context).pop(),
                          style: OutlinedButton.styleFrom(
                            foregroundColor: colorScheme.onSurface,
                            padding: const EdgeInsets.symmetric(
                              horizontal: 20,
                              vertical: 12,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: const Text('取消'),
                        ),
                        const SizedBox(width: 16),
                        // AI 解析 _callAI();
                        ElevatedButton.icon(
                          onPressed: () async {
                            await showDialog(
                              context: context,
                              builder: (BuildContext context) {
                                return ProjectDialog(service: service);
                              },
                            );

                            _callAI();
                          },
                          icon: Icon(
                            Icons.mic_none_rounded,
                            color: Colors.white,
                          ),
                          label: Text(
                            'AI解析',
                            style: TextStyle(color: Colors.white),
                          ),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.orangeAccent,
                            padding: EdgeInsets.symmetric(
                              horizontal: 20,
                              vertical: 12,
                            ),
                          ),
                        ),

                        const SizedBox(width: 16),

                        FilledButton(
                          onPressed: _aiResponded
                              ? () async {
                                  ScaffoldMessenger.of(
                                    context,
                                  ).clearSnackBars();
                                  // TODO: 创建项目

                                  // 将AI大模型返回的数据转换为json格式

                                  // 创建新项目
                                  // final newProject = Project(
                                  //   name: "项目名称——测试",
                                  //   location: 'locationController.text',
                                  //   date: 'dateController.text',
                                  //   signalEntryPoint:
                                  //      ' signalEntryPointController.text',
                                  //   startPoint: 'startPointController.text',
                                  //   diameter: 'diameterController.text',
                                  //   thickness: 'thicknessController.text',
                                  //   material:' materialController.text',
                                  //   medium: 'mediumController.text',
                                  //   coating: 'coatingController.text',
                                  //   soilResistivity:
                                  //       'soilResistivityController.text',
                                  //   workingFrequency:
                                  //       'workingFrequencyController.text',
                                  //   initialCurrent:
                                  //       'initialCurrentController.text',
                                  //   detectionDirection:
                                  //       'detectionDirectionController.text',
                                  // );

                                  // 获取AI大模型返回的数据
                                  final aiResponseJson=_rightController.text;

                                  final uuid=Uuid();

                                  // 解析JSON
                                  final Map<String, dynamic> projectData = jsonDecode(aiResponseJson);
                                  // 创建新项目（字段缺失时自动填充"——"）
                                  final newProject = Project(
                                    name: projectData['name'] ?? "——",
                                    uniqueId: uuid.v4(),//创建唯一编码
                                    location: projectData['location'] ?? "——",
                                    date: projectData['date'] ?? "——",
                                    signalEntryPoint: projectData['signalEntryPoint'] ?? "——",
                                    startPoint: projectData['startPoint'] ?? "——",
                                    diameter: projectData['diameter'] ?? "——",
                                    thickness: projectData['thickness'] ?? "——",
                                    material: projectData['material'] ?? "——",
                                    medium: projectData['medium'] ?? "——",
                                    coating: projectData['coating'] ?? "——",
                                    soilResistivity: projectData['soilResistivity'] ?? "——",
                                    workingFrequency: projectData['workingFrequency'] ?? "——",
                                    initialCurrent: projectData['initialCurrent'] ?? "——",
                                    detectionDirection: projectData['detectionDirection'] ?? "——",
                                  );

                                  // 打开数据库连接
                                  final Database db = await openDatabase(
                                    join(
                                      await getDatabasesPath(),
                                      'projects_database_0507.db',
                                    ),
                                    version: 1,
                                  );

                                  // 插入新项目到数据库
                                  await db.insert(
                                    'projects',
                                    newProject.toMap(),
                                    conflictAlgorithm:
                                        ConflictAlgorithm.replace,
                                  );

                                  // 更新项目列表
                                  Navigator.of(context).pop();
                                  if (context.mounted) {
                                    // 调用项目管理页面的 loadProjects 方法刷新列表
                                    final state = context.findAncestorStateOfType<ProjectManagementPageState>();
                                    if (state != null) {
                                      await state.loadProjects();
                                    }
                                  }

                                  // 提示用户项目创建成功了
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: Row(
                                        children: [
                                          Icon(
                                            Icons.check_circle_outline,
                                            color: Colors.white,
                                          ),
                                          const SizedBox(width: 12),
                                          const Text('项目创建成功'),
                                        ],
                                      ),
                                      behavior: SnackBarBehavior.floating,
                                      backgroundColor: Colors.green.shade600,
                                      duration: const Duration(seconds: 2),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      margin: const EdgeInsets.all(16),
                                    ),
                                  );
                                  // Navigator.of(context).pop();
                                  // TODO: 添加项目管理点击事件
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(builder: (context) => ProjectManagementPage()),
                                  );
                                }
                              : null,
                          style: FilledButton.styleFrom(
                            backgroundColor: colorScheme.primary,
                            foregroundColor: colorScheme.onPrimary,
                            padding: const EdgeInsets.symmetric(
                              horizontal: 20,
                              vertical: 12,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            disabledBackgroundColor: colorScheme.primary
                                .withOpacity(0.3),
                          ),
                          child: Row(
                            children: [
                              const Text('创建项目'),
                              const SizedBox(width: 8),
                              Icon(Icons.arrow_forward, size: 16),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
            ),
          ),
        ),
      ),
    );
  }
}
