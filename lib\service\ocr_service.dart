import 'dart:io';
import 'package:camera/camera.dart';
// import 'package:tesseract_ocr/tesseract_ocr.dart';
// import 'package:tesseract_ocr/ocr_engine_config.dart';
import '../utils/image_utils.dart';
import 'package:path_provider/path_provider.dart';

import 'package:image_picker/image_picker.dart';
import 'package:google_mlkit_text_recognition/google_mlkit_text_recognition.dart';
import 'dart:async';

class OCRService {
  static List<CameraDescription>? _cameras;

  /// 初始化相机设备
  static Future<void> initializeCameras() async {
    try {
      _cameras = await availableCameras();
      print('[CAMERA] 找到 ${_cameras!.length} 个相机设备');
    } catch (e) {
      print('[CAMERA] 相机初始化失败: $e');
      throw Exception('无法获取相机设备列表');
    }
  }

  static Future<String> processImageBymlkit(inputImage) async {

    String extractedText = "";
    // final textRecognizer = TextRecognizer(script: TextRecognitionScript.chinese);
    final textRecognizer = TextRecognizer(script: TextRecognitionScript.latin);
    final RecognizedText recognizedText = await textRecognizer.processImage(inputImage);

    String text = recognizedText.text;

    print("text 识别结果: "+text);
    
    for (TextBlock block in recognizedText.blocks) {
      //each block of text/section of text
      final String text = block.text;

      print("block of text: ");
      print(text);

      for (TextLine line in block.lines) {
        //each line within a text block
        for (TextElement element in line.elements) {
          //each word within a line
          extractedText += element.text + " ";
        }
      }
    }
    extractedText += "\n\n";

    return extractedText;
  }

  /// 获取可用相机列表
  static List<CameraDescription> get availableCamerasList {
    if (_cameras == null || _cameras!.isEmpty) {
      throw Exception('相机未初始化或没有可用设备');
    }
    return _cameras!;
  }

  /// 识别仪表图像中的数字（含小数点）
  static Future<String> recognizeDigits(File image) async {

    try {
      print('[OCR] 开始图像预处理');
      final timestamp = DateTime.now().millisecondsSinceEpoch;

      // 获取应用文档目录
      final directory = await getApplicationDocumentsDirectory();
      final picturesDir = Directory('${directory.path}/Pictures');
      if (!await picturesDir.exists()) {
        await picturesDir.create(recursive: true);
      }

      try {
        // 保存原始图片到私有存储
        final originalPath = '${picturesDir.path}/original_$timestamp.jpg';
        await image.copy(originalPath);
        print('[OCR] 原始图片已保存至: $originalPath');
      } catch (e) {
        print('[OCR] 原始图片保存失败: $e');
      }

      // 剪切左下角仪表数字
      final leftDownDigitsImage = await ImageUtils.cropMeterDigits(
        image,
        480,
        720,
        480,
        360,
      );

      try {
        // 保存预处理后的图片到私有存储
        final processedImgCropPath =
            '${picturesDir.path}/processedImgCrop_$timestamp.jpg';

        await leftDownDigitsImage?.copy(processedImgCropPath);
        print('[OCR] 图片裁切处理结果已保存至: $processedImgCropPath');
      } catch (e) {
        print('[OCR] 图片裁切处理保存失败: $e');
      }

      // TODO 待进一步研究：预处理方案1；预处理方案2
      final processedImage = await ImageUtils.preprocessForOCR2(
        leftDownDigitsImage!,
      );

      try {
        // 保存预处理后的图片到私有存储
        final processedPath = '${picturesDir.path}/preProcessed_$timestamp.jpg';
        await processedImage.copy(processedPath);
        print('[OCR] 预处理图片已保存至: $processedPath');
      } catch (e) {
        print('[OCR] 预处理图片保存失败: $e');
      }

      // 使用示例
      // final croppedImageBytes = await cropMeterDigits();
      // if (croppedImageBytes != null) {
      //   String digits = await TesseractOCR.extractText(
      //     croppedImageBytes,
      //     config: OCRConfig(args: {'tessedit_pageseg_mode': '7'}),
      //   );
      //   print("识别结果: $digits");
      // }

      print('[OCR] 执行OCR识别');
      // 调用Tesseract OCR并获取完整响应
      // final response = await TesseractOcr.extractText(processedImage.path);

      // 不用预处理，直接处理
      // final response = await TesseractOcr.extractText(
      //   image.path,
      //   config: tesseractConfig,
      // );

      final inputImage = InputImage.fromFile(processedImage);
      final response = await processImageBymlkit(inputImage);

      print('[OCR] 原始OCR响应: $response');

      // 检查响应类型并提取文本
      String recognizedText = '';
      // 直接处理响应为字符串
      recognizedText = response.toString();
      print('[OCR] 原始识别文本: $recognizedText');

      print('[OCR] 识别结果: $recognizedText');

      // 解析并返回有效数字
      return _parseDigits(recognizedText);
    } catch (e, stackTrace) {
      print('OCR识别失败: $e');
      print('堆栈信息: $stackTrace');
      return '';
    }
  }

  /// 解析OCR结果，提取有效数字
  static String _parseDigits(String text) {
    print('[OCR] 原始解析文本: $text');

    // 清理OCR结果中的噪声字符
    final cleanText = text.replaceAll(RegExp(r'[^0-9\.]'), '');
    print('[OCR] 清理后文本: $cleanText');

    // 匹配数字模式（含小数点）
    final regExp = RegExp(r'\d*\.?\d+');
    final match = regExp.firstMatch(cleanText);

    final result = match?.group(0) ?? '';
    print('[OCR] 解析后的数字: $result');
    return result;
  }
}
