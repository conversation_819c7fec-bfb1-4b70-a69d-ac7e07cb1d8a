package com.example.a325_lya_1000

import android.app.ActivityManager
import android.app.admin.DevicePolicyManager
import android.content.BroadcastReceiver
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.BatteryManager
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.view.KeyEvent
import android.view.View
import android.view.WindowManager
import android.view.accessibility.AccessibilityEvent
import android.view.accessibility.AccessibilityManager
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel

class MainActivity : FlutterActivity() {
    private val CHANNEL = "com.example.a325_lya_1000/kiosk"
    private val BATTERY_CHANNEL = "com.example.a325_lya_1000/battery"
    private var isKioskModeEnabled = false

    private lateinit var devicePolicyManager: DevicePolicyManager
    private lateinit var adminComponent: ComponentName

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        // Kiosk模式通道
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL).setMethodCallHandler { call, result ->
            handleKioskMethodCall(call, result)
        }

        // 电池状态通道
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, BATTERY_CHANNEL).setMethodCallHandler { call, result ->
            handleBatteryMethodCall(call, result)
        }
    }

    private fun handleKioskMethodCall(call: io.flutter.plugin.common.MethodCall, result: io.flutter.plugin.common.MethodChannel.Result) {
        when (call.method) {
                "enableKioskMode" -> {
                    enableKioskMode()
                    result.success(true)
                }
                "disableKioskMode" -> {
                    disableKioskMode()
                    result.success(true)
                }
                "isKioskModeEnabled" -> {
                    result.success(isKioskModeEnabled)
                }
                "startLockTask" -> {
                    startLockTask()
                    result.success(true)
                }
                "stopLockTask" -> {
                    stopLockTask()
                    result.success(true)
                }
                "startLockTaskSilent" -> {
                    startLockTaskWithoutPrompt()
                    result.success(true)
                }
                "enableDeviceAdmin" -> {
                    enableDeviceAdmin()
                    result.success(true)
                }
                "isDeviceAdminEnabled" -> {
                    result.success(isDeviceAdminEnabled())
                }
                "disableDeviceAdmin" -> {
                    disableDeviceAdmin()
                    result.success(true)
                }
                "shutdownDevice" -> {
                    shutdownDevice()
                    result.success(true)
                }
                "rebootDevice" -> {
                    rebootDevice()
                    result.success(true)
                }
            else -> {
                result.notImplemented()
            }
        }
    }

    private fun handleBatteryMethodCall(call: io.flutter.plugin.common.MethodCall, result: io.flutter.plugin.common.MethodChannel.Result) {
        when (call.method) {
            "getBatteryInfo" -> {
                getBatteryInfo(result)
            }
            else -> {
                result.notImplemented()
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 初始化设备管理员
        devicePolicyManager = getSystemService(Context.DEVICE_POLICY_SERVICE) as DevicePolicyManager
        adminComponent = ComponentName(this, DeviceAdminReceiver::class.java)

        // 启用kiosk模式
        enableKioskMode()

        // 延迟启动锁定任务模式，避免系统提示
        Handler(Looper.getMainLooper()).postDelayed({
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                startLockTaskWithoutPrompt()
            }
        }, 1000) // 延迟1秒
    }

    private fun enableKioskMode() {
        isKioskModeEnabled = true

        // 隐藏系统UI
        hideSystemUI()

        // 保持屏幕常亮
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)

        // 禁用键盘锁
        window.addFlags(WindowManager.LayoutParams.FLAG_DISMISS_KEYGUARD)
        window.addFlags(WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED)
        window.addFlags(WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON)
    }

    private fun disableKioskMode() {
        isKioskModeEnabled = false

        // 显示系统UI
        showSystemUI()

        // 移除屏幕常亮标志
        window.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
    }

    private fun hideSystemUI() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            // Android 11+ (API 30+)
            window.setDecorFitsSystemWindows(false)
            window.insetsController?.let { controller ->
                controller.hide(android.view.WindowInsets.Type.statusBars() or android.view.WindowInsets.Type.navigationBars())
                controller.systemBarsBehavior = android.view.WindowInsetsController.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
            }
        } else {
            // Android 10 and below
            @Suppress("DEPRECATION")
            window.decorView.systemUiVisibility = (
                View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
                or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                or View.SYSTEM_UI_FLAG_FULLSCREEN
            )
        }
    }

    private fun showSystemUI() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            // Android 11+ (API 30+)
            window.setDecorFitsSystemWindows(true)
            window.insetsController?.show(android.view.WindowInsets.Type.statusBars() or android.view.WindowInsets.Type.navigationBars())
        } else {
            // Android 10 and below
            @Suppress("DEPRECATION")
            window.decorView.systemUiVisibility = View.SYSTEM_UI_FLAG_VISIBLE
        }
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        if (isKioskModeEnabled) {
            // 在kiosk模式下禁用某些按键
            when (keyCode) {
                KeyEvent.KEYCODE_BACK,
                KeyEvent.KEYCODE_HOME,
                KeyEvent.KEYCODE_MENU,
                KeyEvent.KEYCODE_APP_SWITCH -> return true
            }
        }
        return super.onKeyDown(keyCode, event)
    }

    override fun onWindowFocusChanged(hasFocus: Boolean) {
        super.onWindowFocusChanged(hasFocus)
        if (hasFocus && isKioskModeEnabled) {
            hideSystemUI()
        }
    }

    override fun onResume() {
        super.onResume()
        if (isKioskModeEnabled) {
            hideSystemUI()
        }
    }

    private fun enableDeviceAdmin() {
        if (!isDeviceAdminEnabled()) {
            val intent = Intent(DevicePolicyManager.ACTION_ADD_DEVICE_ADMIN)
            intent.putExtra(DevicePolicyManager.EXTRA_DEVICE_ADMIN, adminComponent)
            intent.putExtra(DevicePolicyManager.EXTRA_ADD_EXPLANATION, "启用设备管理员以支持kiosk模式")
            startActivity(intent)
        }
    }

    private fun isDeviceAdminEnabled(): Boolean {
        return devicePolicyManager.isAdminActive(adminComponent)
    }

    private fun disableDeviceAdmin() {
        if (isDeviceAdminEnabled()) {
            devicePolicyManager.removeActiveAdmin(adminComponent)
        }
    }

    private fun startLockTaskWithoutPrompt() {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                // 检查是否已经在锁定任务模式
                val activityManager = getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    if (activityManager.lockTaskModeState == ActivityManager.LOCK_TASK_MODE_NONE) {
                        // Android 13特殊处理：使用设备所有者权限预配置锁定任务
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                            setupLockTaskForAndroid13()
                        } else {
                            startLockTask()
                        }

                        // 延迟处理可能出现的系统提示
                        Handler(Looper.getMainLooper()).postDelayed({
                            dismissSystemDialogs()
                        }, 500)
                    }
                } else {
                    startLockTask()
                    Handler(Looper.getMainLooper()).postDelayed({
                        dismissSystemDialogs()
                    }, 500)
                }
            }
        } catch (e: Exception) {
            android.util.Log.e("MainActivity", "启动锁定任务模式失败", e)
        }
    }

    private fun dismissSystemDialogs() {
        try {
            // 尝试关闭系统对话框
            val intent = Intent(Intent.ACTION_CLOSE_SYSTEM_DIALOGS)
            sendBroadcast(intent)

            // 模拟点击"GOT IT"按钮（如果存在）
            performGlobalAction()
        } catch (e: Exception) {
            android.util.Log.e("MainActivity", "关闭系统对话框失败", e)
        }
    }

    private fun performGlobalAction() {
        try {
            // 这里可以添加自动点击逻辑
            // 由于安全限制，我们主要依赖于设备管理员权限来避免提示
        } catch (e: Exception) {
            android.util.Log.e("MainActivity", "执行全局操作失败", e)
        }
    }

    private fun setupLockTaskForAndroid13() {
        try {
            android.util.Log.d("MainActivity", "设置Android 13锁定任务模式")

            // 检查设备所有者状态
            if (devicePolicyManager.isDeviceOwnerApp(packageName)) {
                android.util.Log.d("MainActivity", "确认设备所有者状态")

                // 设置锁定任务包列表（仅包含当前应用）
                val lockTaskPackages = arrayOf(packageName)
                devicePolicyManager.setLockTaskPackages(adminComponent, lockTaskPackages)
                android.util.Log.d("MainActivity", "已设置锁定任务包列表: ${lockTaskPackages.contentToString()}")

                // 设置用户限制以防止退出锁定任务模式
                devicePolicyManager.addUserRestriction(adminComponent, android.os.UserManager.DISALLOW_SAFE_BOOT)

                // 延迟启动锁定任务模式
                Handler(Looper.getMainLooper()).postDelayed({
                    try {
                        startLockTask()
                        android.util.Log.d("MainActivity", "Android 13锁定任务模式启动成功")

                        // 额外的系统提示处理
                        Handler(Looper.getMainLooper()).postDelayed({
                            dismissSystemDialogsForAndroid13()
                        }, 200)
                    } catch (e: Exception) {
                        android.util.Log.e("MainActivity", "Android 13锁定任务启动失败", e)
                    }
                }, 100)

            } else {
                android.util.Log.w("MainActivity", "应用不是设备所有者，使用标准锁定任务模式")
                startLockTask()
            }
        } catch (e: Exception) {
            android.util.Log.e("MainActivity", "Android 13锁定任务设置失败", e)
            // 回退到标准模式
            startLockTask()
        }
    }

    private fun dismissSystemDialogsForAndroid13() {
        try {
            // Android 13特殊的系统对话框处理
            val intent = Intent(Intent.ACTION_CLOSE_SYSTEM_DIALOGS)
            intent.putExtra("reason", "homekey")
            sendBroadcast(intent)

            // 尝试使用设备所有者权限强制关闭系统UI
            if (devicePolicyManager.isDeviceOwnerApp(packageName)) {
                // 设置系统UI可见性
                window.decorView.systemUiVisibility = (
                    View.SYSTEM_UI_FLAG_LAYOUT_STABLE or
                    View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION or
                    View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN or
                    View.SYSTEM_UI_FLAG_HIDE_NAVIGATION or
                    View.SYSTEM_UI_FLAG_FULLSCREEN or
                    View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
                )
            }

            android.util.Log.d("MainActivity", "Android 13系统对话框处理完成")
        } catch (e: Exception) {
            android.util.Log.e("MainActivity", "Android 13系统对话框处理失败", e)
        }
    }

    /**
     * 关机设备
     * 需要设备管理员权限或root权限
     */
    private fun shutdownDevice() {
        try {
            android.util.Log.d("MainActivity", "尝试关机设备")

            // 尝试使用系统命令关机
            android.util.Log.d("MainActivity", "尝试使用系统命令关机")
            try {
                val process = Runtime.getRuntime().exec(arrayOf("su", "-c", "reboot -p"))
                process.waitFor()
            } catch (e: Exception) {
                android.util.Log.w("MainActivity", "su命令关机失败，尝试其他方法", e)
                // 如果su命令失败，尝试使用系统关机命令
                try {
                    val process = Runtime.getRuntime().exec("reboot -p")
                    process.waitFor()
                } catch (ex: Exception) {
                    android.util.Log.w("MainActivity", "系统关机命令失败，尝试Intent方式", ex)
                    throw ex
                }
            }
        } catch (e: Exception) {
            android.util.Log.e("MainActivity", "系统命令关机失败，尝试Intent方式", e)
            // 如果上述方法失败，尝试发送关机Intent
            try {
                val intent = Intent("android.intent.action.ACTION_REQUEST_SHUTDOWN")
                intent.putExtra("android.intent.extra.KEY_CONFIRM", false)
                intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                startActivity(intent)
            } catch (ex: Exception) {
                android.util.Log.e("MainActivity", "发送关机Intent失败", ex)
                // 最后尝试使用PowerManager
                try {
                    val powerManager = getSystemService(Context.POWER_SERVICE) as android.os.PowerManager
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
                        // 在较新的Android版本中，需要系统权限才能关机
                        android.util.Log.w("MainActivity", "PowerManager关机需要系统权限，可能无法执行")
                    }
                } catch (pmEx: Exception) {
                    android.util.Log.e("MainActivity", "PowerManager关机失败", pmEx)
                }
            }
        }
    }

    /**
     * 重启设备
     * 需要设备管理员权限或root权限
     */
    private fun rebootDevice() {
        try {
            android.util.Log.d("MainActivity", "尝试重启设备")

            if (devicePolicyManager.isDeviceOwnerApp(packageName)) {
                // 使用设备所有者权限重启
                android.util.Log.d("MainActivity", "使用设备所有者权限重启")
                devicePolicyManager.reboot(adminComponent)
            } else {
                // 尝试使用系统命令重启
                android.util.Log.d("MainActivity", "尝试使用系统命令重启")
                val process = Runtime.getRuntime().exec(arrayOf("su", "-c", "reboot"))
                process.waitFor()
            }
        } catch (e: Exception) {
            android.util.Log.e("MainActivity", "重启失败", e)
            // 如果上述方法失败，尝试发送重启Intent
            try {
                val intent = Intent(Intent.ACTION_REBOOT)
                intent.putExtra("nowait", 1)
                intent.putExtra("interval", 1)
                intent.putExtra("window", 0)
                sendBroadcast(intent)
            } catch (ex: Exception) {
                android.util.Log.e("MainActivity", "发送重启Intent失败", ex)
            }
        }
    }

    /**
     * 获取电池信息
     */
    private fun getBatteryInfo(result: io.flutter.plugin.common.MethodChannel.Result) {
        try {
            val batteryManager = getSystemService(Context.BATTERY_SERVICE) as BatteryManager

            // 获取电池电量百分比
            val level = batteryManager.getIntProperty(BatteryManager.BATTERY_PROPERTY_CAPACITY)

            // 获取充电状态
            val intentFilter = IntentFilter(Intent.ACTION_BATTERY_CHANGED)
            val batteryStatus = registerReceiver(null, intentFilter)

            val status = batteryStatus?.getIntExtra(BatteryManager.EXTRA_STATUS, -1) ?: -1
            val stateString = when (status) {
                BatteryManager.BATTERY_STATUS_CHARGING -> "charging"
                BatteryManager.BATTERY_STATUS_DISCHARGING -> "discharging"
                BatteryManager.BATTERY_STATUS_NOT_CHARGING -> "not_charging"
                BatteryManager.BATTERY_STATUS_FULL -> "full"
                else -> "unknown"
            }

            val batteryInfo = mapOf(
                "level" to level,
                "state" to stateString
            )

            android.util.Log.d("MainActivity", "电池信息: ${level}% $stateString")
            result.success(batteryInfo)

        } catch (e: Exception) {
            android.util.Log.e("MainActivity", "获取电池信息失败", e)
            result.error("BATTERY_ERROR", "获取电池信息失败: ${e.message}", null)
        }
    }
}
