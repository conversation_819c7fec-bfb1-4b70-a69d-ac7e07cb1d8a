import 'package:flutter/material.dart';

class UsageGuidePage extends StatelessWidget {
  const UsageGuidePage({Key? key}) : super(key: key);

  Widget sectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16.0),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 22,
          fontWeight: FontWeight.bold,
          color: Colors.blue[800],
        ),
      ),
    );
  }

  Widget sectionContentWithIcon(IconData icon, String content) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8),
      elevation: 3,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Icon(icon, size: 36, color: Colors.blue[700]),
            SizedBox(width: 12),
            Expanded(
              child: Text(
                content,
                style: TextStyle(fontSize: 16, height: 1.5),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget stepWithNumber(int step, String description) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CircleAvatar(
            radius: 14,
            backgroundColor: Colors.blue[700],
            child: Text(
              '$step',
              style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
            ),
          ),
          SizedBox(width: 12),
          Expanded(
            child: Text(
              description,
              style: TextStyle(fontSize: 16, height: 1.5),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('软件使用说明'),
        backgroundColor: Colors.blue[700],
      ),
      body: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
        color: Colors.grey[50],
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              sectionTitle('一、软件简介'),
              sectionContentWithIcon(
                Icons.info_outline,
                '本软件是一款管道检测项目管理工具，支持项目的新增、编辑、删除和查看，方便用户管理多个检测项目。软件集成了AI录入功能，提升数据录入效率。',
              ),

              sectionTitle('二、主要功能'),
              sectionContentWithIcon(Icons.list_alt, '项目列表：显示所有已保存的项目，支持新建项目。'),
              sectionContentWithIcon(Icons.edit, '项目详情编辑：查看和编辑项目详细信息。'),
              sectionContentWithIcon(Icons.save, '项目保存：保存新建或编辑的项目。'),
              sectionContentWithIcon(Icons.delete, '项目删除：删除选中的项目。'),
              sectionContentWithIcon(Icons.smart_toy, 'AI录入：通过AI辅助快速录入项目数据。'),
              sectionContentWithIcon(Icons.file_download, '导出功能：暂未实现，敬请期待。'),
              sectionContentWithIcon(Icons.exit_to_app, '退出：关闭当前页面返回。'),

              sectionTitle('三、操作流程示例'),
              stepWithNumber(1, '打开软件，左侧显示项目列表，右侧显示项目详情。'),
              stepWithNumber(2, '点击“新建项目”，填写项目详细信息。'),
              stepWithNumber(3, '点击“保存”按钮，保存项目。'),
              stepWithNumber(4, '点击左侧已有项目名称，查看或编辑项目详情。'),
              stepWithNumber(5, '需要删除项目时，选中项目后点击“删除”。'),
              stepWithNumber(6, '需要AI辅助录入时，点击“AI录入”按钮，按照提示操作。'),
              stepWithNumber(7, '完成后点击“退出”返回。'),

              sectionTitle('四、数据存储'),
              sectionContentWithIcon(
                Icons.storage,
                '项目数据存储在本地SQLite数据库中，每个项目拥有唯一的ID，当前选中项目的信息会缓存以便快速访问。',
              ),

              sectionTitle('五、系统要求'),
              sectionContentWithIcon(
                Icons.computer,
                '支持Flutter运行环境，联网状态下可使用AI录入功能。',
              ),

              sectionTitle('六、注意事项'),
              sectionContentWithIcon(
                Icons.warning_amber_outlined,
                '删除项目操作不可恢复，请谨慎操作。AI录入功能依赖网络，可能受网络影响。导出功能尚未实现。',
              ),

              SizedBox(height: 30),
              Center(
                child: ElevatedButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue[700],
                    padding:
                    EdgeInsets.symmetric(horizontal: 40, vertical: 14),
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8)),
                  ),
                  child: Text(
                    '返回',
                    style: TextStyle(fontSize: 18, color: Colors.white),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}