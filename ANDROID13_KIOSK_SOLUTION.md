# Android 13 Kiosk模式"App is pinned"提示解决方案

## 问题分析

在Android 13上，即使应用已设置为设备所有者，系统仍可能显示"App is pinned"提示。这是因为：

1. **Android 13安全策略更严格** - 对锁定任务模式的控制更加严格
2. **新的权限模型** - 需要额外的权限和配置
3. **系统UI变化** - 系统UI处理机制发生变化

## 解决方案

### 第一步：确认设备所有者状态

```bash
adb shell dpm list-owners
```

应该看到类似输出：
```
1 owner:
User  0: admin=com.example.a325_lya_1000/.DeviceAdminReceiver,DeviceOwner,Affiliated
```

### 第二步：使用Android 13专用设置脚本

运行 `android13_kiosk_setup.bat` 脚本，它会：

1. **验证Android版本**
2. **安装应用**
3. **设置设备所有者**
4. **配置Android 13特定设置**：
   - 设置锁定任务包
   - 禁用系统导航栏
   - 设置用户限制
   - 禁用状态栏下拉

### 第三步：手动配置（如果脚本失败）

#### 1. 设置锁定任务包
```bash
adb shell dpm set-lock-task-packages com.example.a325_lya_1000/.DeviceAdminReceiver com.example.a325_lya_1000
```

#### 2. 配置沉浸式模式
```bash
adb shell settings put global policy_control immersive.full=com.example.a325_lya_1000
```

#### 3. 设置用户限制
```bash
adb shell dpm add-user-restriction com.example.a325_lya_1000/.DeviceAdminReceiver no_safe_boot
adb shell dpm add-user-restriction com.example.a325_lya_1000/.DeviceAdminReceiver no_debugging_features
```

#### 4. 禁用状态栏
```bash
adb shell dpm set-status-bar-disabled com.example.a325_lya_1000/.DeviceAdminReceiver true
```

### 第四步：验证配置

运行 `android13_troubleshoot.bat` 检查所有设置是否正确。

## 代码层面的改进

### 1. Android 13特殊处理

应用现在包含专门针对Android 13的代码：

```kotlin
private fun setupLockTaskForAndroid13() {
    if (devicePolicyManager.isDeviceOwnerApp(packageName)) {
        // 设置锁定任务包列表
        val lockTaskPackages = arrayOf(packageName)
        devicePolicyManager.setLockTaskPackages(adminComponent, lockTaskPackages)
        
        // 设置用户限制
        devicePolicyManager.addUserRestriction(adminComponent, UserManager.DISALLOW_SAFE_BOOT)
        
        // 延迟启动锁定任务模式
        Handler(Looper.getMainLooper()).postDelayed({
            startLockTask()
            dismissSystemDialogsForAndroid13()
        }, 100)
    }
}
```

### 2. 增强的系统对话框处理

```kotlin
private fun dismissSystemDialogsForAndroid13() {
    // Android 13特殊的系统对话框处理
    val intent = Intent(Intent.ACTION_CLOSE_SYSTEM_DIALOGS)
    intent.putExtra("reason", "homekey")
    sendBroadcast(intent)
    
    // 强制设置系统UI可见性
    window.decorView.systemUiVisibility = (
        View.SYSTEM_UI_FLAG_LAYOUT_STABLE or
        View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION or
        View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN or
        View.SYSTEM_UI_FLAG_HIDE_NAVIGATION or
        View.SYSTEM_UI_FLAG_FULLSCREEN or
        View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
    )
}
```

## 故障排除

### 问题1：仍然显示"App is pinned"提示

**解决方案A：重新配置设备所有者**
```bash
adb shell dpm remove-active-admin com.example.a325_lya_1000/.DeviceAdminReceiver
adb shell dpm set-device-owner com.example.a325_lya_1000/.DeviceAdminReceiver
```

**解决方案B：清除应用数据**
```bash
adb shell pm clear com.example.a325_lya_1000
adb install -r build\app\outputs\flutter-apk\app-debug.apk
# 重新运行设置脚本
```

**解决方案C：制造商特定设置**
- 小米设备：关闭"MIUI优化"
- 华为设备：关闭"纯净模式"
- 三星设备：关闭"Knox安全"相关功能

### 问题2：设备所有者设置失败

**原因分析：**
- 设备上有用户账户
- 设备未恢复出厂设置
- 有其他设备管理员应用

**解决方案：**
1. 完全恢复出厂设置
2. 跳过Google账户设置
3. 立即设置设备所有者

### 问题3：应用无法启动锁定任务模式

**检查命令：**
```bash
adb shell dumpsys activity | findstr "mLockTaskModeState"
```

**解决方案：**
```bash
# 强制设置沉浸式模式
adb shell settings put global policy_control immersive.full=*
adb shell wm overscan 0,0,0,0
```

## 最佳实践

### 1. 部署流程

1. **设备准备**：恢复出厂设置
2. **跳过设置**：不添加任何账户
3. **启用调试**：开启USB调试
4. **运行脚本**：使用 `android13_kiosk_setup.bat`
5. **验证配置**：使用 `android13_troubleshoot.bat`
6. **测试功能**：重启设备测试

### 2. 验证清单

- [ ] 设备所有者状态正确
- [ ] 锁定任务包已设置
- [ ] 用户限制已配置
- [ ] 状态栏已禁用
- [ ] 沉浸式模式已启用
- [ ] 应用可正常启动
- [ ] 无系统提示出现

### 3. 维护建议

- 定期检查设备所有者状态
- 监控系统更新对配置的影响
- 建立设备配置备份机制
- 培训操作人员故障排除流程

## 技术支持

如果按照以上步骤仍无法解决问题，请：

1. 运行 `android13_troubleshoot.bat` 收集诊断信息
2. 检查设备制造商特定设置
3. 考虑使用不同的Android 13设备进行测试
4. 联系技术支持提供详细的设备信息和错误日志

## 总结

Android 13的kiosk模式配置比早期版本更复杂，但通过正确的配置和专用脚本，可以完全消除"App is pinned"系统提示，实现真正的无干扰kiosk体验。
