import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:a325_lya_1000/main.dart';

void main() {
  group('Main Page Power Management Tests', () {
    testWidgets('Main page should have power management dropdown', (WidgetTester tester) async {
      // 构建应用
      await tester.pumpWidget(const MyApp());
      await tester.pumpAndSettle();

      // 查找电源管理按钮
      final powerButton = find.byIcon(Icons.power_settings_new);
      expect(powerButton, findsOneWidget);

      // 点击电源管理按钮打开下拉菜单
      await tester.tap(powerButton);
      await tester.pumpAndSettle();

      // 验证下拉菜单项
      expect(find.text('重启设备'), findsOneWidget);
      expect(find.text('关机'), findsOneWidget);

      // 验证图标
      expect(find.byIcon(Icons.refresh), findsOneWidget);
      expect(find.byIcon(Icons.power_off), findsOneWidget);
    });

    testWidgets('Shutdown menu item should trigger shutdown dialog', (WidgetTester tester) async {
      await tester.pumpWidget(const MyApp());
      await tester.pumpAndSettle();

      // 点击电源管理按钮
      final powerButton = find.byIcon(Icons.power_settings_new);
      await tester.tap(powerButton);
      await tester.pumpAndSettle();

      // 点击关机选项
      await tester.tap(find.text('关机'));
      await tester.pumpAndSettle();

      // 验证关机确认对话框
      expect(find.text('确认关机'), findsOneWidget);
      expect(find.textContaining('您确定要关闭设备吗？'), findsOneWidget);
    });

    testWidgets('Reboot menu item should trigger reboot dialog', (WidgetTester tester) async {
      await tester.pumpWidget(const MyApp());
      await tester.pumpAndSettle();

      // 点击电源管理按钮
      final powerButton = find.byIcon(Icons.power_settings_new);
      await tester.tap(powerButton);
      await tester.pumpAndSettle();

      // 点击重启选项
      await tester.tap(find.text('重启设备'));
      await tester.pumpAndSettle();

      // 验证重启确认对话框
      expect(find.text('确认重启'), findsOneWidget);
      expect(find.textContaining('您确定要重启设备吗？'), findsOneWidget);
    });

    testWidgets('Power management dropdown should have correct styling', (WidgetTester tester) async {
      await tester.pumpWidget(const MyApp());
      await tester.pumpAndSettle();

      // 查找PopupMenuButton
      final popupMenuButton = find.byType(PopupMenuButton<String>);
      expect(popupMenuButton, findsOneWidget);

      // 验证按钮属性
      final button = tester.widget<PopupMenuButton<String>>(popupMenuButton);
      expect(button.tooltip, '电源管理');
      expect(button.color, Colors.white);
    });

    testWidgets('Power management menu items should have correct colors', (WidgetTester tester) async {
      await tester.pumpWidget(const MyApp());
      await tester.pumpAndSettle();

      // 打开下拉菜单
      final powerButton = find.byIcon(Icons.power_settings_new);
      await tester.tap(powerButton);
      await tester.pumpAndSettle();

      // 查找菜单项中的图标
      final refreshIcon = find.byIcon(Icons.refresh);
      final powerOffIcon = find.byIcon(Icons.power_off);

      expect(refreshIcon, findsOneWidget);
      expect(powerOffIcon, findsOneWidget);

      // 验证图标颜色（通过查找具有特定颜色的Icon widget）
      final refreshIconWidget = tester.widget<Icon>(refreshIcon);
      final powerOffIconWidget = tester.widget<Icon>(powerOffIcon);

      expect(refreshIconWidget.color, const Color(0xFF2C3E50));
      expect(powerOffIconWidget.color, const Color(0xFFE74C3C));
    });
  });
}
