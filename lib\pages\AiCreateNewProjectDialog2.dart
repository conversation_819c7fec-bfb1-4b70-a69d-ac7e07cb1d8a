// FILEPATH: D:/untitled/lib/pages/AiCreateNewProjectDialog.dart

import 'package:flutter/material.dart';
import '../service/openai_client.dart';
import 'package:get_storage/get_storage.dart';

class ProjectDialog extends StatefulWidget {
  final OpenAIService service;

  const ProjectDialog({Key? key, required this.service}) : super(key: key);

  @override
  _ProjectDialogState createState() => _ProjectDialogState();
}

class _ProjectDialogState extends State<ProjectDialog> {
  final TextEditingController _leftController = TextEditingController();
  final TextEditingController _rightController = TextEditingController();
  final GetStorage _storage = GetStorage();

  bool _loading = false;

  @override
  void initState() {
    super.initState();
    _leftController.text = _storage.read('projectDescForAi') ?? '';
    _rightController.text = _storage.read('projectDescByAi') ?? '';
  }

  Future<void> _callAI() async {
    final input = _leftController.text.trim();
    if (input.isEmpty) {
      _rightController.clear();
      _storage.remove('projectDescByAi');
      return;
    }
    setState(() {
      _loading = true;
    });

    final messages = [
      {
        'role': 'user',
        'content':'''请根据文段内容，按照如下所示的结构返回。\n\n
1. 项目名称: 32701部队管道检测
2. 检测地点: 房山汽油管道
3. 检测日期: 2.25
4. 信号供入点: 测试桩
5. 起测点位置: 测试桩
6. 管径: 219mm
7. 壁厚: 7mm
8. 管材: 20号钢
9. 管内介质: 汽油
10. 防腐层: 3LPE
11. 土壤电阻率: 50
12. 工作频率: 128
13. 初始电流值: 1000
14. 检测方向: 逆流\n\n 对于无法提炼的信息，标记为：待补充。\n\n待提炼的信息：\n\n'''+ input,
      }
    ];

    try {
      final response = await widget.service.chatCompletion(
        messages: messages,
        model: 'glm4:9b',
      );
      _rightController.text = response;
      _storage.write('projectDescByAi', response);
    } catch (e) {
      _rightController.text = '调用AI失败: $e';
      _storage.remove('projectDescByAi');
    } finally {
      setState(() {
        _loading = false;
      });
    }
  }

  @override
  void dispose() {
    _storage.write('projectDescForAi', _leftController.text.trim());
    _storage.write('projectDescByAi', _rightController.text.trim());
    _leftController.dispose();
    _rightController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Dialog(
      insetPadding: const EdgeInsets.all(24),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        width: 720,
        height: 480,
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: theme.colorScheme.surface,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          children: [
            // 标题栏
            Padding(
              padding: const EdgeInsets.only(bottom: 24),
              child: Row(
                children: [
                  Icon(Icons.add_circle_outline, color: theme.primaryColor),
                  const SizedBox(width: 12),
                  Text(
                    '新建项目',
                    style: theme.textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: theme.primaryColor,
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 左侧输入区
                  Expanded(
                    child: _buildInputSection(theme),
                  ),
                  const SizedBox(width: 24),
                  // 右侧AI返回区
                  Expanded(
                    child: _buildOutputSection(theme),
                  ),
                ],
              ),
            ),
            // 按钮栏
            Padding(
              padding: const EdgeInsets.only(top: 24),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: Text('退出', style: TextStyle(color: theme.colorScheme.secondary)),
                  ),
                  const SizedBox(width: 16),
                  ElevatedButton(
                    onPressed: () {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: const Text('AI已经协助新建了项目'),
                          behavior: SnackBarBehavior.floating,
                          backgroundColor: theme.colorScheme.primary,
                        ),
                      );
                      Navigator.of(context).pop();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: theme.colorScheme.primary,
                      foregroundColor: theme.colorScheme.onPrimary,
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                    ),
                    child: const Text('确定'),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInputSection(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.description_outlined, color: theme.colorScheme.primary, size: 20),
            const SizedBox(width: 8),
            Text(
              '项目信息描述',
              style: theme.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Expanded(
          child: TextField(
            controller: _leftController,
            maxLines: null,
            decoration: InputDecoration(
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: theme.colorScheme.outline),
              ),
              hintText: '请输入项目描述...',
              hintStyle: TextStyle(color: theme.hintColor),
              contentPadding: const EdgeInsets.all(16),
              suffixIcon: _loading
                  ? Padding(
                padding: const EdgeInsets.all(16.0),
                child: SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(theme.colorScheme.primary),
                  ),
                ),
              )
                  : null,
            ),
            onChanged: (value) {
              // ... existing onChanged logic ...
            },
          ),
        ),
      ],
    );
  }

  Widget _buildOutputSection(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.check_circle_outline, color: theme.colorScheme.primary, size: 20),
            const SizedBox(width: 8),
            Text(
              '项目信息确认',
              style: theme.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Expanded(
          child: TextField(
            controller: _rightController,
            maxLines: null,
            readOnly: true,
            decoration: InputDecoration(
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: theme.colorScheme.outline),
              ),
              contentPadding: const EdgeInsets.all(16),
              filled: true,
              fillColor: theme.colorScheme.surfaceVariant,
            ),
          ),
        ),
      ],
    );
  }
}