# 设备所有者模式设置指南

## 概述

为了完全避免"App is pinned"系统提示，我们可以将应用设置为设备所有者模式。这样可以获得最高级别的设备控制权限，完全消除系统提示。

## 前提条件

⚠️ **重要提醒：** 设备所有者模式需要在**全新设备**或**恢复出厂设置后的设备**上设置，且设备上不能有任何Google账户。

## 设置步骤

### 方法一：ADB命令设置（推荐）

1. **准备设备**
   ```bash
   # 确保设备已连接并启用USB调试
   adb devices
   ```

2. **安装应用**
   ```bash
   # 安装APK文件
   adb install app-release.apk
   ```

3. **设置设备所有者**
   ```bash
   # 设置应用为设备所有者
   adb shell dpm set-device-owner com.example.a325_lya_1000/.DeviceAdminReceiver
   ```

4. **验证设置**
   ```bash
   # 检查设备所有者状态
   adb shell dpm list-owners
   ```

### 方法二：通过NFC配置（适用于批量部署）

1. **创建NFC配置文件**
   - 使用Android Device Policy应用
   - 配置设备所有者信息
   - 生成NFC标签

2. **设备配置**
   - 恢复出厂设置
   - 在设置向导中使用NFC标签
   - 自动配置为设备所有者

### 方法三：通过QR码配置

1. **生成QR码**
   ```json
   {
     "android.app.extra.PROVISIONING_DEVICE_ADMIN_COMPONENT_NAME": "com.example.a325_lya_1000/.DeviceAdminReceiver",
     "android.app.extra.PROVISIONING_DEVICE_ADMIN_PACKAGE_DOWNLOAD_LOCATION": "https://your-server.com/app-release.apk",
     "android.app.extra.PROVISIONING_SKIP_ENCRYPTION": true
   }
   ```

2. **设备配置**
   - 恢复出厂设置
   - 在设置向导中扫描QR码
   - 自动下载并配置应用

## 设备所有者权限优势

设置为设备所有者后，应用将获得以下权限：

- ✅ **无系统提示的锁定任务模式**
- ✅ **完全的设备控制权限**
- ✅ **防止应用被卸载**
- ✅ **系统级别的kiosk模式**
- ✅ **自动处理系统对话框**

## 验证脚本

创建以下脚本来验证设备所有者状态：

```bash
#!/bin/bash
# check_device_owner.sh

echo "检查设备连接..."
adb devices

echo "检查设备所有者状态..."
OWNER=$(adb shell dpm list-owners)

if [[ $OWNER == *"com.example.a325_lya_1000"* ]]; then
    echo "✅ 设备所有者设置成功"
    echo "应用包名: com.example.a325_lya_1000"
    echo "设备管理员: DeviceAdminReceiver"
else
    echo "❌ 设备所有者未设置或设置失败"
    echo "当前所有者: $OWNER"
fi

echo "检查应用安装状态..."
APP_INSTALLED=$(adb shell pm list packages | grep com.example.a325_lya_1000)

if [[ -n $APP_INSTALLED ]]; then
    echo "✅ 应用已安装"
else
    echo "❌ 应用未安装"
fi
```

## 故障排除

### 常见错误及解决方案

#### 错误1: "Not allowed to set the device owner"
**原因：** 设备上已有用户账户或应用

**解决方案：**
1. 恢复出厂设置
2. 跳过Google账户设置
3. 重新执行设置命令

#### 错误2: "Device owner is already set"
**原因：** 设备已有其他设备所有者

**解决方案：**
```bash
# 清除现有设备所有者
adb shell dpm remove-active-admin com.example.a325_lya_1000/.DeviceAdminReceiver
# 重新设置
adb shell dpm set-device-owner com.example.a325_lya_1000/.DeviceAdminReceiver
```

#### 错误3: "Package not found"
**原因：** 应用未正确安装

**解决方案：**
1. 确认APK已安装
2. 检查包名是否正确
3. 重新安装应用

## 移除设备所有者

如果需要移除设备所有者状态：

```bash
# 方法1: 通过ADB
adb shell dpm remove-active-admin com.example.a325_lya_1000/.DeviceAdminReceiver

# 方法2: 恢复出厂设置（彻底清除）
adb shell am broadcast -a android.intent.action.MASTER_CLEAR
```

## 批量部署建议

对于大量设备的部署：

1. **准备标准化镜像**
   - 配置好的系统镜像
   - 预装应用和设置
   - 自动化配置脚本

2. **使用MDM解决方案**
   - Android Enterprise
   - Samsung Knox
   - 其他企业移动设备管理平台

3. **NFC/QR码批量配置**
   - 统一的配置标签
   - 自动化部署流程
   - 质量检查脚本

## 安全注意事项

⚠️ **重要警告：**

- 设备所有者具有最高权限，请谨慎使用
- 确保物理设备安全
- 定期备份设备配置
- 建立设备管理策略
- 培训操作人员

## 技术支持

如果在设置过程中遇到问题：

1. 检查设备Android版本兼容性
2. 确认USB调试已启用
3. 验证ADB连接正常
4. 查看详细错误日志

```bash
# 获取详细日志
adb logcat | grep -E "(DevicePolicyManager|DeviceAdminReceiver)"
```
