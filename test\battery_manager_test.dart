import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:a325_lya_1000/utils/battery_manager.dart';
import 'package:a325_lya_1000/widgets/battery_status_widget.dart';

void main() {
  group('BatteryManager Tests', () {
    late BatteryManager batteryManager;

    setUp(() {
      batteryManager = BatteryManager.instance;
    });

    test('BatteryManager should be a singleton', () {
      final instance1 = BatteryManager.instance;
      final instance2 = BatteryManager.instance;
      expect(instance1, same(instance2));
    });

    test('BatteryManager should have initialize method', () {
      expect(batteryManager.initialize, isA<Function>());
    });

    test('BatteryManager should have monitoring methods', () {
      expect(batteryManager.startMonitoring, isA<Function>());
      expect(batteryManager.stopMonitoring, isA<Function>());
      expect(batteryManager.isMonitoring, isA<bool>());
    });

    test('BatteryInfo should have correct properties', () {
      final batteryInfo = BatteryInfo(
        level: 85,
        state: BatteryState.charging,
        isLowBattery: false,
        lastUpdated: DateTime.now(),
      );

      expect(batteryInfo.level, 85);
      expect(batteryInfo.state, BatteryState.charging);
      expect(batteryInfo.isLowBattery, false);
      expect(batteryInfo.stateText, '充电中');
      expect(batteryInfo.stateIcon, '⚡');
    });

    test('BatteryInfo should correctly identify low battery', () {
      final lowBatteryInfo = BatteryInfo(
        level: 15,
        state: BatteryState.discharging,
        isLowBattery: true,
        lastUpdated: DateTime.now(),
      );

      expect(lowBatteryInfo.isLowBattery, true);
      expect(lowBatteryInfo.stateText, '放电中');
      expect(lowBatteryInfo.stateIcon, '🪫');
    });

    test('BatteryState enum should have correct display texts', () {
      expect(BatteryInfo(
        level: 100,
        state: BatteryState.full,
        isLowBattery: false,
        lastUpdated: DateTime.now(),
      ).stateText, '已充满');

      expect(BatteryInfo(
        level: 50,
        state: BatteryState.notCharging,
        isLowBattery: false,
        lastUpdated: DateTime.now(),
      ).stateText, '未充电');

      expect(BatteryInfo(
        level: 0,
        state: BatteryState.unknown,
        isLowBattery: true,
        lastUpdated: DateTime.now(),
      ).stateText, '未知');
    });
  });

  group('BatteryStatusWidget Tests', () {
    testWidgets('BatteryStatusWidget should display unknown state when no battery info', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: BatteryStatusWidget(),
          ),
        ),
      );

      // 等待widget构建完成
      await tester.pumpAndSettle();

      // 应该显示未知状态
      expect(find.byIcon(Icons.battery_unknown), findsOneWidget);
      expect(find.text('--'), findsOneWidget);

      // 清理定时器
      BatteryManager.instance.stopMonitoring();
    });

    testWidgets('BatteryStatusWidget should have proper styling', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: BatteryStatusWidget(),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 查找Container组件
      final containerFinder = find.byType(Container);
      expect(containerFinder, findsAtLeastNWidgets(1));

      // 验证Container有正确的装饰
      final container = tester.widget<Container>(containerFinder.first);
      expect(container.decoration, isA<BoxDecoration>());

      final decoration = container.decoration as BoxDecoration;
      expect(decoration.borderRadius, isA<BorderRadius>());
      expect(decoration.border, isA<Border>());

      // 清理定时器
      BatteryManager.instance.stopMonitoring();
    });

    testWidgets('BatteryStatusWidget should display battery level text', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: BatteryStatusWidget(),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 应该有文本显示
      expect(find.byType(Text), findsAtLeastNWidgets(1));

      // 清理定时器
      BatteryManager.instance.stopMonitoring();
    });

    testWidgets('BatteryStatusWidget should have Row layout', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: BatteryStatusWidget(),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 应该有Row布局
      expect(find.byType(Row), findsAtLeastNWidgets(1));

      // 清理定时器
      BatteryManager.instance.stopMonitoring();
    });
  });
}
