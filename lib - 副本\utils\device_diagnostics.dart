import 'dart:io';

/// 设备诊断工具
/// 用于检查串口设备状态和权限
class DeviceDiagnostics {
  
  /// 诊断串口GPS设备
  static Future<DiagnosticResult> diagnoseSerialGPS() async {
    final result = DiagnosticResult();
    
    print('🔍 开始诊断串口GPS设备...');
    
    // 1. 检查设备文件是否存在
    await _checkDeviceExists(result);
    
    // 2. 检查设备权限
    await _checkDevicePermissions(result);
    
    // 3. 检查USB设备列表
    await _checkUSBDevices(result);
    
    // 4. 尝试读取设备信息
    await _testDeviceAccess(result);
    
    // 5. 生成诊断报告
    _generateReport(result);
    
    return result;
  }
  
  /// 检查设备文件是否存在
  static Future<void> _checkDeviceExists(DiagnosticResult result) async {
    try {
      final devicePaths = [
        '/dev/ttyUSB0',
        '/dev/ttyUSB1', 
        '/dev/ttyUSB2',
        '/dev/ttyUSB3',
        '/dev/ttyACM0',
        '/dev/ttyACM1',
      ];
      
      for (final path in devicePaths) {
        final file = File(path);
        if (await file.exists()) {
          result.availableDevices.add(path);
          print('✅ 发现设备: $path');
        }
      }
      
      if (result.availableDevices.isEmpty) {
        result.issues.add('❌ 未发现任何串口设备');
        result.suggestions.add('💡 请检查GPS模块是否正确连接');
      } else {
        result.checks.add('✅ 设备文件检查通过');
      }
      
    } catch (e) {
      result.issues.add('❌ 设备文件检查失败: $e');
    }
  }
  
  /// 检查设备权限
  static Future<void> _checkDevicePermissions(DiagnosticResult result) async {
    try {
      // 检查当前用户权限
      final whoamiResult = await Process.run('whoami', []);
      final currentUser = whoamiResult.stdout.toString().trim();
      result.systemInfo['current_user'] = currentUser;
      
      // 检查用户组
      final groupsResult = await Process.run('groups', []);
      final userGroups = groupsResult.stdout.toString().trim();
      result.systemInfo['user_groups'] = userGroups;
      
      print('👤 当前用户: $currentUser');
      print('👥 用户组: $userGroups');
      
      // 检查设备权限
      for (final device in result.availableDevices) {
        final lsResult = await Process.run('ls', ['-l', device]);
        if (lsResult.exitCode == 0) {
          final permissions = lsResult.stdout.toString().trim();
          result.devicePermissions[device] = permissions;
          print('🔐 $device 权限: $permissions');
        }
      }
      
      result.checks.add('✅ 权限检查完成');
      
    } catch (e) {
      result.issues.add('❌ 权限检查失败: $e');
      result.suggestions.add('💡 可能需要root权限或添加用户到dialout组');
    }
  }
  
  /// 检查USB设备列表
  static Future<void> _checkUSBDevices(DiagnosticResult result) async {
    try {
      // 检查lsusb命令
      final lsusbResult = await Process.run('lsusb', []);
      if (lsusbResult.exitCode == 0) {
        final usbDevices = lsusbResult.stdout.toString();
        result.systemInfo['usb_devices'] = usbDevices;
        print('🔌 USB设备列表:');
        print(usbDevices);
        result.checks.add('✅ USB设备检查完成');
      }
    } catch (e) {
      result.issues.add('⚠️  无法获取USB设备列表: $e');
    }
  }
  
  /// 测试设备访问
  static Future<void> _testDeviceAccess(DiagnosticResult result) async {
    for (final device in result.availableDevices) {
      try {
        print('🧪 测试访问设备: $device');
        
        // 尝试读取设备（超时5秒）
        final process = await Process.start('timeout', ['5', 'cat', device]);
        
        // 等待一小段时间看是否有数据
        await Future.delayed(Duration(seconds: 2));
        
        process.kill();
        await process.exitCode;
        
        result.accessTests[device] = '✅ 可访问';
        print('✅ $device 访问测试通过');
        
      } catch (e) {
        result.accessTests[device] = '❌ 访问失败: $e';
        print('❌ $device 访问测试失败: $e');
      }
    }
  }
  
  /// 生成诊断报告
  static void _generateReport(DiagnosticResult result) {
    print('\n📋 ===== 串口GPS诊断报告 =====');
    
    print('\n✅ 检查通过项:');
    for (final check in result.checks) {
      print('  $check');
    }
    
    print('\n🔍 发现的设备:');
    if (result.availableDevices.isEmpty) {
      print('  无');
    } else {
      for (final device in result.availableDevices) {
        print('  $device');
      }
    }
    
    print('\n🧪 访问测试结果:');
    if (result.accessTests.isEmpty) {
      print('  无测试');
    } else {
      result.accessTests.forEach((device, status) {
        print('  $device: $status');
      });
    }
    
    if (result.issues.isNotEmpty) {
      print('\n❌ 发现的问题:');
      for (final issue in result.issues) {
        print('  $issue');
      }
    }
    
    if (result.suggestions.isNotEmpty) {
      print('\n💡 建议解决方案:');
      for (final suggestion in result.suggestions) {
        print('  $suggestion');
      }
    }
    
    print('\n📊 系统信息:');
    result.systemInfo.forEach((key, value) {
      print('  $key: $value');
    });
    
    print('\n🔐 设备权限:');
    result.devicePermissions.forEach((device, permissions) {
      print('  $device: $permissions');
    });
    
    print('\n===== 诊断报告结束 =====\n');
  }
  
  /// 获取修复建议
  static List<String> getFixSuggestions(DiagnosticResult result) {
    final suggestions = <String>[];
    
    if (result.availableDevices.isEmpty) {
      suggestions.addAll([
        '1. 检查GPS模块是否正确连接到USB端口',
        '2. 尝试重新插拔USB连接',
        '3. 检查GPS模块是否正常工作（LED指示灯）',
        '4. 尝试连接到其他USB端口',
      ]);
    }
    
    if (result.accessTests.values.any((status) => status.contains('访问失败'))) {
      suggestions.addAll([
        '5. 尝试以root权限运行应用',
        '6. 将用户添加到dialout组: sudo usermod -a -G dialout \$USER',
        '7. 重启设备后重试',
        '8. 检查SELinux策略设置',
      ]);
    }
    
    suggestions.addAll([
      '9. 确认GPS模块波特率设置为115200',
      '10. 检查GPS模块是否支持NMEA协议',
      '11. 使用串口调试工具测试设备',
    ]);
    
    return suggestions;
  }
}

/// 诊断结果类
class DiagnosticResult {
  final List<String> checks = [];
  final List<String> issues = [];
  final List<String> suggestions = [];
  final List<String> availableDevices = [];
  final Map<String, String> accessTests = {};
  final Map<String, String> systemInfo = {};
  final Map<String, String> devicePermissions = {};
  
  bool get hasIssues => issues.isNotEmpty;
  bool get hasDevices => availableDevices.isNotEmpty;
  
  @override
  String toString() {
    return 'DiagnosticResult{devices: $availableDevices, issues: ${issues.length}, checks: ${checks.length}}';
  }
}
