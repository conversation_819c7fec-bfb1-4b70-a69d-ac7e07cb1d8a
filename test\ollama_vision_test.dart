import 'package:flutter_test/flutter_test.dart';
import 'package:get_storage/get_storage.dart';
import 'package:a325_lya_1000/service/ollama_vision_service.dart';

void main() {
  group('OllamaVisionService Tests', () {
    late OllamaVisionService service;

    setUpAll(() async {
      // 初始化GetStorage用于测试
      await GetStorage.init();
    });

    setUp(() {
      service = OllamaVisionService.instance;
    });

    test('should have default configuration', () {
      expect(service.serverUrl, equals('https://cup-ollama.ailer.ltd'));
      expect(service.apiKey, equals('123456'));
      expect(service.model, equals('qwen2.5vl:7b'));
    });

    test('should update configuration', () {
      const testUrl = 'https://test.example.com';
      const testKey = 'test-key';
      const testModel = 'test-model';

      service.setServerUrl(testUrl);
      service.setApiKey(testKey);
      service.setModel(testModel);

      expect(service.serverUrl, equals(testUrl));
      expect(service.apiKey, equals(testKey));
      expect(service.model, equals(testModel));
    });

    test('should reset to defaults', () {
      // 先设置一些自定义值
      service.setServerUrl('https://custom.example.com');
      service.setApiKey('custom-key');
      service.setModel('custom-model');

      // 重置为默认值
      service.resetToDefaults();

      expect(service.serverUrl, equals('https://cup-ollama.ailer.ltd'));
      expect(service.apiKey, equals('123456'));
      expect(service.model, equals('qwen2.5vl:7b'));
    });

    test('should extract numbers from text', () {
      final service = OllamaVisionService.instance;
      
      // 使用反射或创建测试方法来测试私有方法
      // 这里我们测试整个流程的结果
      expect(true, isTrue); // 占位测试
    });

    test('should handle connection test', () async {
      // 测试连接功能
      final result = await service.testConnection();
      
      // 由于这是一个网络请求，我们只验证它返回一个布尔值
      expect(result, isA<bool>());
    });

    test('should get service status', () async {
      final status = await service.getServiceStatus();
      
      expect(status, isA<Map<String, dynamic>>());
      expect(status.containsKey('connected'), isTrue);
      expect(status.containsKey('serverUrl'), isTrue);
      expect(status.containsKey('model'), isTrue);
      expect(status.containsKey('lastCheck'), isTrue);
    });
  });

  group('VisionRecognitionResult Tests', () {
    test('should create result from json', () {
      final json = {
        'originalText': 'Test text 123',
        'extractedNumber': '123',
        'confidence': 0.95,
        'timestamp': '2024-01-01T12:00:00.000Z',
      };

      final result = VisionRecognitionResult.fromJson(json);

      expect(result.originalText, equals('Test text 123'));
      expect(result.extractedNumber, equals('123'));
      expect(result.confidence, equals(0.95));
      expect(result.timestamp, isA<DateTime>());
    });

    test('should convert result to json', () {
      final result = VisionRecognitionResult(
        originalText: 'Test text 456',
        extractedNumber: '456',
        confidence: 0.88,
        timestamp: DateTime.parse('2024-01-01T12:00:00.000Z'),
      );

      final json = result.toJson();

      expect(json['originalText'], equals('Test text 456'));
      expect(json['extractedNumber'], equals('456'));
      expect(json['confidence'], equals(0.88));
      expect(json['timestamp'], equals('2024-01-01T12:00:00.000Z'));
    });
  });
}
