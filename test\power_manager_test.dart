import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:a325_lya_1000/utils/power_manager.dart';
import 'package:a325_lya_1000/utils/kiosk_manager.dart';

void main() {
  group('PowerManager Tests', () {
    late PowerManager powerManager;

    setUp(() {
      powerManager = PowerManager.instance;
    });

    testWidgets('PowerManager should show shutdown confirmation dialog', (WidgetTester tester) async {
      // 创建一个测试应用
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) {
                return ElevatedButton(
                  onPressed: () {
                    powerManager.shutdownDevice(context);
                  },
                  child: const Text('关机'),
                );
              },
            ),
          ),
        ),
      );

      // 点击关机按钮
      await tester.tap(find.text('关机'));
      await tester.pumpAndSettle();

      // 验证确认对话框是否显示
      expect(find.text('确认关机'), findsOneWidget);
      expect(find.textContaining('您确定要关闭设备吗？'), findsOneWidget);
      expect(find.text('关机'), findsAtLeastNWidgets(1)); // 按钮中的文本
    });

    testWidgets('PowerManager should show reboot confirmation dialog', (WidgetTester tester) async {
      // 创建一个测试应用
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) {
                return ElevatedButton(
                  onPressed: () {
                    powerManager.rebootDevice(context);
                  },
                  child: const Text('重启'),
                );
              },
            ),
          ),
        ),
      );

      // 点击重启按钮
      await tester.tap(find.text('重启'));
      await tester.pumpAndSettle();

      // 验证确认对话框是否显示
      expect(find.text('确认重启'), findsOneWidget);
      expect(find.textContaining('您确定要重启设备吗？'), findsOneWidget);
      expect(find.text('重启'), findsAtLeastNWidgets(1)); // 按钮中的文本
    });

    testWidgets('PowerManager dialog should have proper styling', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) {
                return ElevatedButton(
                  onPressed: () {
                    powerManager.shutdownDevice(context);
                  },
                  child: const Text('关机'),
                );
              },
            ),
          ),
        ),
      );

      await tester.tap(find.text('关机'));
      await tester.pumpAndSettle();

      // 验证对话框样式
      final alertDialog = tester.widget<AlertDialog>(find.byType(AlertDialog));
      expect(alertDialog.backgroundColor, Colors.white);
      expect(alertDialog.shape, isA<RoundedRectangleBorder>());

      // 验证警告图标
      expect(find.byIcon(Icons.warning_amber_rounded), findsOneWidget);
    });
  });

  group('KioskManager Power Methods Tests', () {
    late KioskManager kioskManager;

    setUp(() {
      kioskManager = KioskManager.instance;
    });

    test('KioskManager should have shutdown and reboot methods', () {
      // 验证方法存在
      expect(kioskManager.shutdownDevice, isA<Function>());
      expect(kioskManager.rebootDevice, isA<Function>());
    });
  });
}
