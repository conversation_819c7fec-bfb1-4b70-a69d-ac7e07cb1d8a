package com.example.a325_lya_1000

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.os.Handler
import android.os.Looper
import android.util.Log

class BootReceiver : BroadcastReceiver() {
    override fun onReceive(context: Context, intent: Intent) {
        Log.d("BootReceiver", "Boot completed, starting app...")
        
        when (intent.action) {
            Intent.ACTION_BOOT_COMPLETED,
            "android.intent.action.QUICKBOOT_POWERON",
            "com.htc.intent.action.QUICKBOOT_POWERON" -> {
                // 延迟启动应用，确保系统完全启动
                Handler(Looper.getMainLooper()).postDelayed({
                    startApp(context)
                }, 5000) // 延迟5秒
            }
        }
    }
    
    private fun startApp(context: Context) {
        try {
            // 启动后台服务
            val serviceIntent = Intent(context, KioskService::class.java)
            context.startForegroundService(serviceIntent)

            // 启动主应用
            val appIntent = Intent(context, MainActivity::class.java).apply {
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
                addFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP)
            }
            context.startActivity(appIntent)
            Log.d("BootReceiver", "App and service started successfully")
        } catch (e: Exception) {
            Log.e("BootReceiver", "Failed to start app", e)
        }
    }
}
