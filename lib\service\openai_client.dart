// FILEPATH: D:/untitled/lib/openai_client.dart

import 'dart:async';
import 'dart:convert';
import 'package:dio/dio.dart';

class OpenAIService {
  final String baseURL;
  final String apiKey;
  final Dio _dio;

  OpenAIService({
    required this.baseURL,
    required this.apiKey,
  }) : _dio = Dio(BaseOptions(
    baseUrl: baseURL,
    connectTimeout: const Duration(seconds: 10),
    receiveTimeout: const Duration(seconds: 180),
    headers: {
      'Authorization': 'Bearer $apiKey',
    },
    contentType: 'application/json; charset=utf-8',
    responseType: ResponseType.stream,
  ));

  /// 发送聊天请求，支持流式输出
  ///
  /// [messages] 是消息列表，格式为 [{'role': 'user|assistant|system', 'content': '...'}]
  /// [model] 指定使用的模型名称，默认 'qwen-plus'
  ///
  /// 返回一个Stream<String>，每条数据为模型生成的文本片段
  Stream<String> chatCompletionStream({
    required List<Map<String, String>> messages,
    String model = 'glm4:9b',
  }) async* {
    final data = {
      'messages': messages,
      'model': model,
      'stream': true,
    };

    try {
      final response = await _dio.post('', data: data);
      await for (String chunk in processStreamResponse(response.data.stream)) {
        yield chunk;
      }
    } catch (e) {
      print('请求异常: $e');
      rethrow;
    }
  }

  /// 发送聊天请求，直接返回完整回复，不使用流式输出
  ///
  /// [messages] 是消息列表，格式为 [{'role': 'user|assistant|system', 'content': '...'}]
  /// [model] 指定使用的模型名称，默认 'qwen-plus'
  ///
  /// 返回一个Future<String>，包含模型生成的完整回复文本
  Future<String> chatCompletion({
    required List<Map<String, String>> messages,
    String model = 'glm4:9b',
  }) async {
    final data = {
      'messages': messages,
      'model': model,
      'stream': false,
    };

    try {
      final response = await _dio.post(
        '',
        data: data,
        options: Options(responseType: ResponseType.json),
      );

      if (response.statusCode == 200) {
        final jsonResponse = response.data;
        final choices = jsonResponse['choices'];
        if (choices != null && choices is List && choices.isNotEmpty) {
          return choices[0]['message']['content'] as String;
        } else {
          throw Exception('无效的响应格式');
        }
      } else {
        throw Exception('请求失败: ${response.statusCode}');
      }
    } catch (e) {
      print('请求异常: $e');
      rethrow;
    }
  }

  /// 处理流式响应
  Stream<String> processStreamResponse(Stream<List<int>> stream) async* {
    final buffer = StringBuffer();

    await for (var data in stream) {
      final decodedData = utf8.decode(data);
      final jsonDataList = decodedData.split('data: ')
          .where((element) => element.isNotEmpty)
          .toList();

      for (var element in jsonDataList) {
        if (element.trim() == '[DONE]') break;

        try {
          final json = jsonDecode(element);
          final content = json['choices'][0]['delta']['content'] as String? ?? '';
          final finishReason = json['choices'][0]['finish_reason'] as String? ?? '';

          if (content.isNotEmpty) {
            buffer.write(content);
            yield content;
          }

          if (finishReason == 'stop') {
            break;
          }
        } catch (e) {
          print('Error parsing JSON: $e');
        }
      }
    }
  }
}

// 使用示例
// void main() async {
//   final service = OpenAIService(
//     baseURL: 'https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions',
//     apiKey: '你的API_KEY',
//   );
//
//   final messages = [
//     {'role': 'user', 'content': '你好，介绍一下你自己。'}
//   ];
//
//   // 流式输出示例
//   try {
//     await for (final chunk in service.chatCompletionStream(messages: messages)) {
//       print(chunk);
//     }
//   } catch (e) {
//     print('流式调用接口出错: $e');
//   }
//
//   // 非流式输出示例
//   try {
//     final response = await service.chatCompletion(messages: messages);
//     print('AI回复: $response');
//   } catch (e) {
//     print('非流式调用接口出错: $e');
//   }
// }

//备用代码段
// 1.在类中初始化
// final service = OpenAIService(
//   // baseURL: 'https://openrouter.ai/api/v1/chat/completions',
//   baseURL: 'https://cup-ollama.ailer.ltd/v1/chat/completions',
//   apiKey:'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',
// );

// 2.使用示例
// final messages = [
//   {'role': 'user', 'content': '你好，介绍一下你自己。'},
// ];
//
// try {
// final response = await service.chatCompletion(
// messages: messages,
// model: 'glm4:9b',
// );
// print('AI非流式回复: $response');
// } catch (e) {
// print('非流式调用接口出错: $e');
// }