import 'package:flutter/material.dart';
import '../theme/industrial_theme.dart';

/// 工业级状态指示器
class IndustrialStatusIndicator extends StatelessWidget {
  final String status;
  final String label;
  final IconData? icon;
  final double size;

  const IndustrialStatusIndicator({
    Key? key,
    required this.status,
    required this.label,
    this.icon,
    this.size = 12,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final color = IndustrialTheme.getStatusColor(status);
    
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: size,
          height: size,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: color.withValues(alpha: 0.3),
                blurRadius: 4,
                spreadRadius: 1,
              ),
            ],
          ),
        ),
        const SizedBox(width: 8),
        if (icon != null) ...[
          Icon(icon, size: 16, color: color),
          const SizedBox(width: 4),
        ],
        Text(
          label,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: IndustrialTheme.textPrimary,
          ),
        ),
      ],
    );
  }
}

/// 工业级数据卡片
class IndustrialDataCard extends StatelessWidget {
  final String title;
  final String value;
  final String? unit;
  final IconData icon;
  final Color? iconColor;
  final VoidCallback? onTap;
  final bool isLoading;

  const IndustrialDataCard({
    Key? key,
    required this.title,
    required this.value,
    this.unit,
    required this.icon,
    this.iconColor,
    this.onTap,
    this.isLoading = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: IndustrialStyles.cardRadius,
        child: Padding(
          padding: const EdgeInsets.all(IndustrialStyles.spacingM),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: (iconColor ?? IndustrialTheme.primaryBlue).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      icon,
                      color: iconColor ?? IndustrialTheme.primaryBlue,
                      size: IndustrialStyles.iconM,
                    ),
                  ),
                  const SizedBox(width: IndustrialStyles.spacingS),
                  Expanded(
                    child: Text(
                      title,
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: IndustrialStyles.spacingM),
              if (isLoading)
                const SizedBox(
                  height: 20,
                  width: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              else
                Row(
                  crossAxisAlignment: CrossAxisAlignment.baseline,
                  textBaseline: TextBaseline.alphabetic,
                  children: [
                    Text(
                      value,
                      style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: IndustrialTheme.primaryBlue,
                      ),
                    ),
                    if (unit != null) ...[
                      const SizedBox(width: 4),
                      Text(
                        unit!,
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                    ],
                  ],
                ),
            ],
          ),
        ),
      ),
    );
  }
}

/// 工业级功能按钮
class IndustrialActionButton extends StatelessWidget {
  final String label;
  final IconData icon;
  final VoidCallback? onPressed;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final bool isLoading;
  final bool isEnabled;
  final double? width;
  final double? height;

  const IndustrialActionButton({
    Key? key,
    required this.label,
    required this.icon,
    this.onPressed,
    this.backgroundColor,
    this.foregroundColor,
    this.isLoading = false,
    this.isEnabled = true,
    this.width,
    this.height,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: width,
      height: height ?? 48,
      child: ElevatedButton.icon(
        onPressed: isEnabled && !isLoading ? onPressed : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: backgroundColor ?? IndustrialTheme.primaryBlue,
          foregroundColor: foregroundColor ?? Colors.white,
          disabledBackgroundColor: IndustrialTheme.textDisabled,
          elevation: isEnabled ? 2 : 0,
          shape: RoundedRectangleBorder(
            borderRadius: IndustrialStyles.buttonRadius,
          ),
        ),
        icon: isLoading
            ? SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    foregroundColor ?? Colors.white,
                  ),
                ),
              )
            : Icon(icon, size: IndustrialStyles.iconM),
        label: Text(
          label,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            letterSpacing: 0.5,
          ),
        ),
      ),
    );
  }
}

/// 工业级信息面板
class IndustrialInfoPanel extends StatelessWidget {
  final String title;
  final List<Widget> children;
  final IconData? icon;
  final Color? headerColor;
  final bool isCollapsible;
  final bool initiallyExpanded;

  const IndustrialInfoPanel({
    Key? key,
    required this.title,
    required this.children,
    this.icon,
    this.headerColor,
    this.isCollapsible = false,
    this.initiallyExpanded = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (isCollapsible) {
      return Card(
        child: ExpansionTile(
          initiallyExpanded: initiallyExpanded,
          leading: icon != null
              ? Icon(icon, color: headerColor ?? IndustrialTheme.primaryBlue)
              : null,
          title: Text(
            title,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: headerColor ?? IndustrialTheme.primaryBlue,
              fontWeight: FontWeight.w600,
            ),
          ),
          children: [
            Padding(
              padding: const EdgeInsets.all(IndustrialStyles.spacingM),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: children,
              ),
            ),
          ],
        ),
      );
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(IndustrialStyles.spacingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                if (icon != null) ...[
                  Icon(
                    icon,
                    color: headerColor ?? IndustrialTheme.primaryBlue,
                    size: IndustrialStyles.iconM,
                  ),
                  const SizedBox(width: IndustrialStyles.spacingS),
                ],
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: headerColor ?? IndustrialTheme.primaryBlue,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: IndustrialStyles.spacingM),
            ...children,
          ],
        ),
      ),
    );
  }
}

/// 工业级数据行
class IndustrialDataRow extends StatelessWidget {
  final String label;
  final String value;
  final IconData? icon;
  final Color? valueColor;
  final bool isHighlighted;

  const IndustrialDataRow({
    Key? key,
    required this.label,
    required this.value,
    this.icon,
    this.valueColor,
    this.isHighlighted = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(
        vertical: IndustrialStyles.spacingS,
        horizontal: IndustrialStyles.spacingS,
      ),
      decoration: isHighlighted
          ? BoxDecoration(
              color: IndustrialTheme.primaryBlue.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(6),
              border: Border.all(
                color: IndustrialTheme.primaryBlue.withValues(alpha: 0.2),
              ),
            )
          : null,
      child: Row(
        children: [
          if (icon != null) ...[
            Icon(
              icon,
              size: IndustrialStyles.iconS,
              color: IndustrialTheme.textSecondary,
            ),
            const SizedBox(width: IndustrialStyles.spacingS),
          ],
          SizedBox(
            width: 80,
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: valueColor ?? IndustrialTheme.textPrimary,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.end,
            ),
          ),
        ],
      ),
    );
  }
}
