import 'dart:math';
import 'package:geolocator/geolocator.dart';
import 'serial_gps_service.dart';

/// GPS定位服务类
/// 提供位置获取、权限检查、距离计算等功能
class GPSService {
  static GPSService? _instance;
  static GPSService get instance => _instance ??= GPSService._();
  
  GPSService._();

  // 存储历史位置点用于计算距离
  final List<Position> _historyPositions = [];

  // 存储串口GPS历史位置点用于计算距离
  final List<GPSData> _serialGPSHistory = [];

  // 串口GPS服务
  final SerialGPSService _serialGPS = SerialGPSService.instance;
  bool _useSerialGPS = false;
  
  /// 检查定位权限
  Future<bool> checkLocationPermission() async {
    bool serviceEnabled;
    LocationPermission permission;

    // 检查定位服务是否开启
    serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      return false;
    }

    // 检查权限状态
    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        return false;
      }
    }
    
    if (permission == LocationPermission.deniedForever) {
      return false;
    }

    return true;
  }

  /// 获取当前位置
  Future<Position?> getCurrentLocation() async {
    try {
      // 检查权限
      bool hasPermission = await checkLocationPermission();
      if (!hasPermission) {
        throw Exception('定位权限未授予');
      }

      // 获取当前位置
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: Duration(seconds: 10),
      );

      // 添加到历史位置列表
      _historyPositions.add(position);
      
      // 只保留最近10个位置点
      if (_historyPositions.length > 10) {
        _historyPositions.removeAt(0);
      }

      return position;
    } catch (e) {
      print('获取位置失败: $e');
      return null;
    }
  }

  /// 计算两点之间的距离（米）
  double calculateDistance(double lat1, double lon1, double lat2, double lon2) {
    return Geolocator.distanceBetween(lat1, lon1, lat2, lon2);
  }

  /// 获取最近三次采集点之间的距离
  List<double> getRecentDistances() {
    List<double> distances = [];

    if (_historyPositions.length < 2) {
      return [-1, -1, -1]; // 不足两个点，返回默认值
    }

    // 计算最近几次位置之间的距离
    int count = min(3, _historyPositions.length - 1);
    for (int i = 0; i < count; i++) {
      int currentIndex = _historyPositions.length - 1 - i;
      int previousIndex = currentIndex - 1;

      if (previousIndex >= 0) {
        Position current = _historyPositions[currentIndex];
        Position previous = _historyPositions[previousIndex];

        double distance = calculateDistance(
          current.latitude,
          current.longitude,
          previous.latitude,
          previous.longitude,
        );

        distances.add(distance);
      }
    }

    // 补齐到3个元素
    while (distances.length < 3) {
      distances.add(-1);
    }

    return distances.take(3).toList();
  }

  /// 记录串口GPS数据点用于距离计算
  void recordSerialGPSPosition(GPSData gpsData) {
    // 添加到历史位置列表
    _serialGPSHistory.add(gpsData);

    // 只保留最近10个位置点
    if (_serialGPSHistory.length > 10) {
      _serialGPSHistory.removeAt(0);
    }
  }

  /// 获取串口GPS最近采集点距离（只返回最近一次距离）
  double getSerialGPSRecentDistance() {
    if (_serialGPSHistory.length < 2) {
      return -1; // 不足两个点，返回默认值
    }

    // 获取最近两个位置点
    GPSData current = _serialGPSHistory.last;
    GPSData previous = _serialGPSHistory[_serialGPSHistory.length - 2];

    // 计算距离
    return calculateDistance(
      current.latitude,
      current.longitude,
      previous.latitude,
      previous.longitude,
    );
  }

  /// 获取位置精度描述
  String getAccuracyDescription(double accuracy) {
    if (accuracy <= 5) {
      return '高精度';
    } else if (accuracy <= 10) {
      return '中等精度';
    } else if (accuracy <= 20) {
      return '低精度';
    } else {
      return '精度较差';
    }
  }

  /// 清除历史位置数据
  void clearHistory() {
    _historyPositions.clear();
  }

  /// 获取历史位置数量
  int get historyCount => _historyPositions.length;

  /// 启用串口GPS
  Future<bool> enableSerialGPS() async {
    try {
      print('[GPS服务] 🛰️ 启用高精度串口GPS');
      final connected = await _serialGPS.connect();
      if (connected) {
        _useSerialGPS = true;
        print('[GPS服务] ✅ 串口GPS已启用');

        // 添加串口GPS数据监听器
        _serialGPS.addListener((gpsData) {
          _printSerialGPSData(gpsData);
        });

        return true;
      } else {
        print('[GPS服务] ❌ 串口GPS启用失败');
        return false;
      }
    } catch (e) {
      print('[GPS服务] ⚠️ 启用串口GPS异常: $e');
      return false;
    }
  }

  /// 禁用串口GPS
  Future<void> disableSerialGPS() async {
    try {
      await _serialGPS.disconnect();
      _useSerialGPS = false;
      print('[GPS服务] 串口GPS已禁用');
    } catch (e) {
      print('[GPS服务] 禁用串口GPS时出错: $e');
    }
  }

  /// 获取串口GPS数据
  GPSData? getSerialGPSData() {
    return _serialGPS.latestData;
  }

  /// 检查串口GPS是否连接
  bool isSerialGPSConnected() {
    return _serialGPS.isConnected;
  }

  /// 打印串口GPS数据
  void _printSerialGPSData(GPSData gpsData) {
    final lat = gpsData.latitude.toStringAsFixed(6);
    final lon = gpsData.longitude.toStringAsFixed(6);
    final alt = gpsData.altitude?.toStringAsFixed(1) ?? '未知';
    final quality = gpsData.fixQuality ?? '标准';

    print('🛰️ [高精度GPS] $lat°, $lon°, ${alt}m | $quality');
  }
}
