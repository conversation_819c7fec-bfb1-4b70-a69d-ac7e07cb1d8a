import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';

/// 网络连接状态管理器
class ConnectivityManager {
  static ConnectivityManager? _instance;
  static ConnectivityManager get instance => _instance ??= ConnectivityManager._();
  
  ConnectivityManager._();

  bool _isConnected = true;
  Timer? _connectivityTimer;
  final List<VoidCallback> _listeners = [];

  /// 当前网络连接状态
  bool get isConnected => _isConnected;

  /// 添加连接状态监听器
  void addListener(VoidCallback listener) {
    _listeners.add(listener);
  }

  /// 移除连接状态监听器
  void removeListener(VoidCallback listener) {
    _listeners.remove(listener);
  }

  /// 开始监听网络连接状态
  void startMonitoring() {
    _connectivityTimer?.cancel();
    // 禁用定时检查以节省功耗
    // _connectivityTimer = Timer.periodic(
    //   const Duration(seconds: 5),
    //   (_) => _checkConnectivity(),
    // );
    _checkConnectivity(); // 立即检查一次
  }

  /// 停止监听网络连接状态
  void stopMonitoring() {
    _connectivityTimer?.cancel();
    _connectivityTimer = null;
  }

  /// 检查网络连接状态
  Future<void> _checkConnectivity() async {
    try {
      final result = await InternetAddress.lookup('www.baidu.com');
      final newStatus = result.isNotEmpty && result[0].rawAddress.isNotEmpty;
      
      if (newStatus != _isConnected) {
        _isConnected = newStatus;
        _notifyListeners();
      }
    } catch (e) {
      if (_isConnected) {
        _isConnected = false;
        _notifyListeners();
      }
    }
  }

  /// 通知所有监听器
  void _notifyListeners() {
    for (final listener in _listeners) {
      listener();
    }
  }

  /// 手动检查网络连接
  Future<bool> checkConnection() async {
    try {
      final result = await InternetAddress.lookup('www.baidu.com');
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  /// 释放资源
  void dispose() {
    stopMonitoring();
    _listeners.clear();
  }
}

/// 网络状态指示器组件
class ConnectivityIndicator extends StatefulWidget {
  final Widget child;
  final bool showOfflineMessage;

  const ConnectivityIndicator({
    Key? key,
    required this.child,
    this.showOfflineMessage = true,
  }) : super(key: key);

  @override
  State<ConnectivityIndicator> createState() => _ConnectivityIndicatorState();
}

class _ConnectivityIndicatorState extends State<ConnectivityIndicator> {
  final ConnectivityManager _connectivityManager = ConnectivityManager.instance;
  bool _isConnected = true;

  @override
  void initState() {
    super.initState();
    _isConnected = _connectivityManager.isConnected;
    _connectivityManager.addListener(_onConnectivityChanged);
    _connectivityManager.startMonitoring();
  }

  @override
  void dispose() {
    _connectivityManager.removeListener(_onConnectivityChanged);
    super.dispose();
  }

  void _onConnectivityChanged() {
    if (mounted) {
      setState(() {
        _isConnected = _connectivityManager.isConnected;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        widget.child,
        if (!_isConnected && widget.showOfflineMessage)
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
              color: Colors.red[700],
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.wifi_off,
                    color: Colors.white,
                    size: 16,
                  ),
                  const SizedBox(width: 8),
                  const Text(
                    '网络连接已断开',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }
}

/// 应用状态管理器
class AppStateManager {
  static AppStateManager? _instance;
  static AppStateManager get instance => _instance ??= AppStateManager._();
  
  AppStateManager._();

  // 应用状态
  bool _isInitialized = false;
  bool _isLoading = false;
  String? _currentError;
  Map<String, dynamic> _appData = {};

  // 状态监听器
  final List<VoidCallback> _stateListeners = [];

  /// 应用是否已初始化
  bool get isInitialized => _isInitialized;

  /// 应用是否正在加载
  bool get isLoading => _isLoading;

  /// 当前错误信息
  String? get currentError => _currentError;

  /// 应用数据
  Map<String, dynamic> get appData => Map.unmodifiable(_appData);

  /// 添加状态监听器
  void addStateListener(VoidCallback listener) {
    _stateListeners.add(listener);
  }

  /// 移除状态监听器
  void removeStateListener(VoidCallback listener) {
    _stateListeners.remove(listener);
  }

  /// 设置初始化状态
  void setInitialized(bool initialized) {
    if (_isInitialized != initialized) {
      _isInitialized = initialized;
      _notifyStateListeners();
    }
  }

  /// 设置加载状态
  void setLoading(bool loading) {
    if (_isLoading != loading) {
      _isLoading = loading;
      _notifyStateListeners();
    }
  }

  /// 设置错误信息
  void setError(String? error) {
    if (_currentError != error) {
      _currentError = error;
      _notifyStateListeners();
    }
  }

  /// 清除错误
  void clearError() {
    setError(null);
  }

  /// 更新应用数据
  void updateAppData(String key, dynamic value) {
    _appData[key] = value;
    _notifyStateListeners();
  }

  /// 获取应用数据
  T? getAppData<T>(String key) {
    return _appData[key] as T?;
  }

  /// 通知状态监听器
  void _notifyStateListeners() {
    for (final listener in _stateListeners) {
      listener();
    }
  }

  /// 重置应用状态
  void reset() {
    _isInitialized = false;
    _isLoading = false;
    _currentError = null;
    _appData.clear();
    _notifyStateListeners();
  }

  /// 释放资源
  void dispose() {
    _stateListeners.clear();
    _appData.clear();
  }
}

/// 数据验证工具类
class DataValidator {
  /// 验证GPS坐标
  static bool isValidGPSCoordinate(double? latitude, double? longitude) {
    if (latitude == null || longitude == null) return false;
    return latitude >= -90 && latitude <= 90 &&
           longitude >= -180 && longitude <= 180;
  }

  /// 验证数字字符串
  static bool isValidNumber(String? value) {
    if (value == null || value.isEmpty) return false;
    return double.tryParse(value) != null;
  }

  /// 验证项目名称
  static bool isValidProjectName(String? name) {
    if (name == null || name.trim().isEmpty) return false;
    return name.trim().length >= 2 && name.trim().length <= 50;
  }

  /// 验证文件路径
  static bool isValidFilePath(String? path) {
    if (path == null || path.isEmpty) return false;
    try {
      final file = File(path);
      return file.existsSync();
    } catch (e) {
      return false;
    }
  }

  /// 清理和验证用户输入
  static String sanitizeInput(String? input) {
    if (input == null) return '';
    return input.trim().replaceAll(RegExp(r'[<>"\'']'), '');
  }
}
