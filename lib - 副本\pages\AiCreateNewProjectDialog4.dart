// FILEPATH: D:/untitled/lib/pages/AiCreateNewProjectDialog.dart

import 'package:flutter/material.dart';
import '../service/openai_client.dart';
import 'package:get_storage/get_storage.dart';

class ProjectDialog extends StatefulWidget {
  final OpenAIService service;

  const ProjectDialog({Key? key, required this.service}) : super(key: key);

  @override
  _ProjectDialogState createState() => _ProjectDialogState();
}

class _ProjectDialogState extends State<ProjectDialog> {
  final TextEditingController _leftController = TextEditingController();
  final TextEditingController _rightController = TextEditingController();
  final GetStorage _storage = GetStorage();

  bool _loading = false;

  @override
  void initState() {
    super.initState();
    _leftController.text = _storage.read('projectDescForAi') ?? '';
    _rightController.text = _storage.read('projectDescByAi') ?? '';
  }

  Future<void> _callAI() async {
    final input = _leftController.text.trim();
    if (input.isEmpty) {
      _rightController.clear();
      _storage.remove('projectDescByAi');
      return;
    }
    setState(() {
      _loading = true;
    });

    final messages = [
      {
        'role': 'user',
        'content':'''请根据文段内容，按照如下所示的结构返回。\n\n
1. 项目名称: 32701部队管道检测
2. 检测地点: 房山汽油管道
3. 检测日期: 2.25
4. 信号供入点: 测试桩
5. 起测点位置: 测试桩
6. 管径: 219mm
7. 壁厚: 7mm
8. 管材: 20号钢
9. 管内介质: 汽油
10. 防腐层: 3LPE
11. 土壤电阻率: 50
12. 工作频率: 128
13. 初始电流值: 1000
14. 检测方向: 逆流\n\n 对于无法提炼的信息，标记为：待补充。\n\n待提炼的信息：\n\n'''+ input,
      }
    ];

    try {
      final response = await widget.service.chatCompletion(
        messages: messages,
        model: 'glm4:9b',
      );
      _rightController.text = response;
      _storage.write('projectDescByAi', response);
    } catch (e) {
      _rightController.text = '调用AI失败: $e';
      _storage.remove('projectDescByAi');
    } finally {
      setState(() {
        _loading = false;
      });
    }
  }

  @override
  void dispose() {
    _storage.write('projectDescForAi', _leftController.text.trim());
    _storage.write('projectDescByAi', _rightController.text.trim());
    _leftController.dispose();
    _rightController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      insetPadding: const EdgeInsets.all(24),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        width: 640,
        height: 360,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          children: [
            // 标题栏
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 12),
              child: Text(
                '新建项目',
                style: Theme.of(context).textTheme.titleMedium,
              ),
            ),
            Expanded(
              child: Row(
                children: [
                  // 左侧输入区
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Padding(
                          padding: EdgeInsets.only(bottom: 8),
                          child: Text(
                            '项目信息描述',
                            style: TextStyle(fontWeight: FontWeight.w600),
                          ),
                        ),
                        Expanded(
                          child: TextField(
                            controller: _leftController,
                            maxLines: null,
                            decoration: InputDecoration(
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              hintText: '请输入项目描述，示例参考：项目描述：本项目为32701部队管道检测，于2月25日对房山汽油管道进行检测。检测点位于测试桩，管道规格为219mm管径、7mm壁厚，采用20号钢管材，内装汽油介质。防腐层使用3LPE，土壤电阻率为50。检测采用逆流方式进行，初始电流值为1000。',
                              contentPadding: const EdgeInsets.all(12),
                              suffixIcon: _loading
                                  ? const Padding(
                                padding: EdgeInsets.all(12.0),
                                child: SizedBox(
                                  width: 16,
                                  height: 16,
                                  child: CircularProgressIndicator(strokeWidth: 2),
                                ),
                              )
                                  : null,
                            ),
                            onChanged: (value) {
                              _storage.write('projectDescForAi', value.trim());
                              if (value.trim().length >= 10) {
                                _callAI();
                              } else {
                                _rightController.clear();
                                _storage.remove('projectDescByAi');
                              }
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 16),
                  // 右侧AI返回区
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Padding(
                          padding: EdgeInsets.only(bottom: 8),
                          child: Text(
                            '项目信息确认',
                            style: TextStyle(fontWeight: FontWeight.w600),
                          ),
                        ),
                        Expanded(
                          child: TextField(
                            controller: _rightController,
                            maxLines: null,
                            readOnly: true,
                            decoration: InputDecoration(
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              contentPadding: const EdgeInsets.all(12),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            // 按钮栏
            Padding(
              padding: const EdgeInsets.only(top: 12),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('退出'),
                  ),
                  const SizedBox(width: 12),
                  ElevatedButton(
                    onPressed: () {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('AI已经协助新建了项目'),
                          behavior: SnackBarBehavior.floating,
                          backgroundColor: Colors.green,
                        ),
                      );
                      Navigator.of(context).pop();
                    },
                    child: const Text('确定'),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}