@echo off
echo ========================================
echo Android 13 Kiosk模式故障排除脚本
echo ========================================
echo.

echo 1. 系统信息检查...
echo Android版本:
adb shell getprop ro.build.version.release

echo.
echo 设备型号:
adb shell getprop ro.product.model

echo.
echo 制造商:
adb shell getprop ro.product.manufacturer

echo.
echo 2. 设备所有者状态检查...
adb shell dpm list-owners
echo.

echo 3. 锁定任务包检查...
adb shell dpm get-lock-task-packages com.example.a325_lya_1000/.DeviceAdminReceiver
echo.

echo 4. 用户限制检查...
echo 检查安全启动限制:
adb shell dpm get-user-restrictions com.example.a325_lya_1000/.DeviceAdminReceiver | findstr no_safe_boot

echo.
echo 检查调试功能限制:
adb shell dpm get-user-restrictions com.example.a325_lya_1000/.DeviceAdminReceiver | findstr no_debugging_features

echo.
echo 5. 状态栏设置检查...
adb shell dpm get-status-bar-disabled com.example.a325_lya_1000/.DeviceAdminReceiver

echo.
echo 6. 应用权限检查...
echo 检查应用权限:
adb shell dumpsys package com.example.a325_lya_1000 | findstr permission

echo.
echo 7. 系统设置检查...
echo 检查沉浸式模式设置:
adb shell settings get global policy_control

echo.
echo 8. 锁定任务模式状态...
echo 当前锁定任务模式状态:
adb shell dumpsys activity | findstr "mLockTaskModeState"

echo.
echo 9. 系统UI状态...
echo 检查系统UI可见性:
adb shell dumpsys window | findstr "mSystemUiVisibility"

echo.
echo ========================================
echo 故障排除建议
echo ========================================
echo.
echo 如果仍然看到"App is pinned"提示:
echo.
echo 方案1: 重新配置设备所有者
echo   adb shell dpm remove-active-admin com.example.a325_lya_1000/.DeviceAdminReceiver
echo   adb shell dpm set-device-owner com.example.a325_lya_1000/.DeviceAdminReceiver
echo.
echo 方案2: 清除应用数据并重新设置
echo   adb shell pm clear com.example.a325_lya_1000
echo   adb install -r build\app\outputs\flutter-apk\app-debug.apk
echo   运行 android13_kiosk_setup.bat
echo.
echo 方案3: 检查制造商特定设置
echo   某些制造商（如小米、华为）可能有额外的安全设置
echo   需要在设置中关闭"MIUI优化"或类似选项
echo.
echo 方案4: 使用ADB强制设置
echo   adb shell settings put global policy_control immersive.full=*
echo   adb shell wm overscan 0,0,0,0
echo.
echo 方案5: 重启设备
echo   adb reboot
echo   等待设备重启后重新测试
echo.
pause
