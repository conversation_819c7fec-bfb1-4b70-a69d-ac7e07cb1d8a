import 'package:flutter/material.dart';
import '../theme/industrial_theme.dart';
import '../widgets/industrial_widgets.dart';
import '../utils/device_status_manager.dart';

/// 设备状态显示组件
/// 用于在首页展示四个设备的连接状态
class DeviceStatusWidget extends StatefulWidget {
  final bool showDetails;
  final VoidCallback? onRefresh;

  const DeviceStatusWidget({Key? key, this.showDetails = false, this.onRefresh})
    : super(key: key);

  @override
  State<DeviceStatusWidget> createState() => _DeviceStatusWidgetState();
}

class _DeviceStatusWidgetState extends State<DeviceStatusWidget> {
  late DeviceStatusManager _statusManager;

  @override
  void initState() {
    super.initState();
    _statusManager = DeviceStatusManager.instance;
    _statusManager.addListener(_onStatusChanged);

    // 停用自动监控以节省功耗
    // 用户可以通过手动刷新按钮来检查设备状态
    // if (!_statusManager.isMonitoring) {
    //   _statusManager.startMonitoring();
    // }
  }

  @override
  void dispose() {
    _statusManager.removeListener(_onStatusChanged);
    super.dispose();
  }

  void _onStatusChanged() {
    if (mounted) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    final deviceStatus = _statusManager.deviceStatus;

    if (widget.showDetails) {
      return _buildDetailedView(deviceStatus);
    } else {
      return _buildCompactView(deviceStatus);
    }
  }

  /// 构建紧凑视图（用于首页状态栏）
  Widget _buildCompactView(Map<String, DeviceInfo> deviceStatus) {
    final systemHealth = _statusManager.getSystemHealthStatus();
    final stats = _statusManager.getStatusStatistics();
    final hasErrors = (stats['error'] ?? 0) > 0;
    final connectedCount = stats['connected'] ?? 0;
    final totalCount = deviceStatus.length;
    final colorScheme = Theme.of(context).colorScheme;
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // 系统状态文字描述
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              children: [
                Icon(Icons.info_outline, size: 14, color: colorScheme.primary),
                const SizedBox(width: 6),
                Text(
                  hasErrors
                      ? '系统异常'
                      : connectedCount == totalCount
                      ? '系统正常'
                      : '部分设备离线',
                  style: TextStyle(
                    color: hasErrors
                        ? IndustrialTheme.errorRed
                        : connectedCount == totalCount
                        ? IndustrialTheme.successGreen
                        : IndustrialTheme.warningAmber,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 2),
            Text(
              hasErrors
                  ? '请检查设备连接'
                  : connectedCount == totalCount
                  ? '所有设备运行正常'
                  : '部分设备需要检查',
              style: TextStyle(
                color: IndustrialTheme.textSecondary,
                fontSize: 11,
              ),
            ),
          ],
        ),
        const SizedBox(width: 16),
        // 系统健康状态指示器（椭圆形，内含四个设备指示灯）
        Container(
          width: 120,
          height: 40,
          decoration: BoxDecoration(
            color: hasErrors
                ? IndustrialTheme.errorRed.withValues(alpha: 0.1)
                : connectedCount == totalCount
                ? IndustrialTheme.successGreen.withValues(alpha: 0.1)
                : IndustrialTheme.warningAmber.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: hasErrors
                  ? IndustrialTheme.errorRed
                  : connectedCount == totalCount
                  ? IndustrialTheme.successGreen
                  : IndustrialTheme.warningAmber,
              width: 1,
            ),
          ),

          child: Stack(
            children: [
              // 中心文字
              Center(
                child: Text(
                  '设备已连接  $connectedCount/$totalCount ',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: hasErrors
                        ? IndustrialTheme.errorRed
                        : connectedCount == totalCount
                        ? IndustrialTheme.successGreen
                        : IndustrialTheme.warningAmber,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    height: 1.2,
                  ),
                ),
              ),

              // 四个设备指示灯（放在椭圆边缘的四个方位）
              // ..._buildDeviceIndicatorsInCircle(deviceStatus),
            ],
          ),
        ),
      ],
    );
  }

  /// 构建椭圆内的设备指示灯
  List<Widget> _buildDeviceIndicatorsInCircle(
    Map<String, DeviceInfo> deviceStatus,
  ) {
    final devices = deviceStatus.entries.toList();
    final positions = [
      {'top': 2.0, 'left': 54.0}, // 上方
      {'top': 15.0, 'right': 8.0}, // 右方
      {'bottom': 2.0, 'left': 54.0}, // 下方
      {'top': 15.0, 'left': 8.0}, // 左方
    ];

    List<Widget> indicators = [];
    for (int i = 0; i < devices.length && i < positions.length; i++) {
      final device = devices[i].value;
      final position = positions[i];

      indicators.add(
        Positioned(
          top: position['top'],
          left: position['left'],
          right: position['right'],
          bottom: position['bottom'],
          child: Tooltip(
            message: '${device.name}: ${_getStatusText(device.status)}',
            child: Container(
              width: 8,
              height: 8,
              decoration: BoxDecoration(
                color: _getStatusColor(device.status),
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: _getStatusColor(
                      device.status,
                    ).withValues(alpha: 0.6),
                    blurRadius: 2,
                    spreadRadius: 0.5,
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    }
    return indicators;
  }

  /// 构建详细视图（用于设备状态页面）
  Widget _buildDetailedView(Map<String, DeviceInfo> deviceStatus) {
    return IndustrialInfoPanel(
      title: '设备连接状态',
      icon: Icons.devices,
      headerColor: IndustrialTheme.primaryBlue,
      children: [
        ...deviceStatus.entries.map((entry) {
          final deviceKey = entry.key;
          final device = entry.value;
          return _buildDeviceDetailCard(deviceKey, device);
        }),
        const SizedBox(height: IndustrialStyles.spacingS),
        Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () {
                  _statusManager.checkAllDevices();
                  widget.onRefresh?.call();
                },
                icon: const Icon(Icons.refresh),
                label: const Text('刷新状态'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: IndustrialTheme.primaryBlue,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
            const SizedBox(width: IndustrialStyles.spacingS),
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () {
                  if (_statusManager.isMonitoring) {
                    _statusManager.stopMonitoring();
                  } else {
                    _statusManager.startMonitoring();
                  }
                },
                icon: Icon(
                  _statusManager.isMonitoring ? Icons.stop : Icons.play_arrow,
                ),
                label: Text(_statusManager.isMonitoring ? '停止监控' : '开始监控'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: _statusManager.isMonitoring
                      ? IndustrialTheme.warningAmber
                      : IndustrialTheme.successGreen,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 构建设备指示器
  Widget _buildDeviceIndicator(
    String name,
    DeviceStatus status, {
    bool compact = false,
  }) {
    Color statusColor;
    IconData statusIcon;

    switch (status) {
      case DeviceStatus.connected:
        statusColor = IndustrialTheme.successGreen;
        statusIcon = Icons.check_circle;
        break;
      case DeviceStatus.disconnected:
        statusColor = IndustrialTheme.warningAmber;
        statusIcon = Icons.error_outline;
        break;
      case DeviceStatus.error:
        statusColor = IndustrialTheme.errorRed;
        statusIcon = Icons.error;
        break;
      case DeviceStatus.checking:
        statusColor = IndustrialTheme.processingPurple;
        statusIcon = Icons.hourglass_empty;
        break;
      case DeviceStatus.unknown:
        statusColor = IndustrialTheme.borderGrey;
        statusIcon = Icons.help_outline;
        break;
    }

    if (compact) {
      return Tooltip(
        message: '$name: ${_getStatusText(status)}',
        child: Container(
          width: 8,
          height: 8,
          decoration: BoxDecoration(
            color: statusColor,
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: statusColor.withValues(alpha: 0.3),
                blurRadius: 2,
                spreadRadius: 1,
              ),
            ],
          ),
        ),
      );
    } else {
      return IndustrialStatusIndicator(
        status: _getStatusText(status),
        label: name,
        icon: statusIcon,
      );
    }
  }

  /// 构建设备详细卡片
  Widget _buildDeviceDetailCard(String deviceKey, DeviceInfo device) {
    return Container(
      margin: const EdgeInsets.only(bottom: IndustrialStyles.spacingS),
      padding: const EdgeInsets.all(IndustrialStyles.spacingS),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: IndustrialTheme.borderGrey, width: 1),
        boxShadow: IndustrialStyles.cardShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              _buildDeviceIcon(deviceKey),
              const SizedBox(width: IndustrialStyles.spacingS),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      device.name,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: IndustrialTheme.textPrimary,
                      ),
                    ),
                    Text(
                      device.description,
                      style: const TextStyle(
                        fontSize: 12,
                        color: IndustrialTheme.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
              _buildDeviceIndicator(device.name, device.status),
            ],
          ),
          if (device.errorMessage != null) ...[
            const SizedBox(height: IndustrialStyles.spacingXS),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: IndustrialTheme.errorRed.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.warning,
                    size: 16,
                    color: IndustrialTheme.errorRed,
                  ),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      device.errorMessage!,
                      style: TextStyle(
                        fontSize: 12,
                        color: IndustrialTheme.errorRed,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
          const SizedBox(height: IndustrialStyles.spacingXS),
          Text(
            '最后检查: ${_formatTime(device.lastChecked)}',
            style: const TextStyle(
              fontSize: 11,
              color: IndustrialTheme.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建设备图标
  Widget _buildDeviceIcon(String deviceKey) {
    IconData icon;
    Color color;

    switch (deviceKey) {
      case 'gps':
        icon = Icons.gps_fixed;
        color = IndustrialTheme.gpsGreen;
        break;
      case 'network':
        icon = Icons.wifi;
        color = IndustrialTheme.primaryBlue;
        break;
      case 'camera':
        icon = Icons.camera_alt;
        color = IndustrialTheme.cameraIndigo;
        break;
      case 'microphone':
        icon = Icons.mic;
        color = IndustrialTheme.accentOrange;
        break;
      default:
        icon = Icons.device_unknown;
        color = IndustrialTheme.borderGrey;
        break;
    }

    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Icon(icon, size: 24, color: color),
    );
  }

  /// 获取状态文本
  String _getStatusText(DeviceStatus status) {
    switch (status) {
      case DeviceStatus.connected:
        return '已连接';
      case DeviceStatus.disconnected:
        return '未连接';
      case DeviceStatus.error:
        return '错误';
      case DeviceStatus.checking:
        return '检查中';
      case DeviceStatus.unknown:
        return '未知';
    }
  }

  /// 获取状态颜色
  Color _getStatusColor(DeviceStatus status) {
    switch (status) {
      case DeviceStatus.connected:
        return IndustrialTheme.successGreen;
      case DeviceStatus.disconnected:
        return IndustrialTheme.warningAmber;
      case DeviceStatus.error:
        return IndustrialTheme.errorRed;
      case DeviceStatus.checking:
        return IndustrialTheme.processingPurple;
      case DeviceStatus.unknown:
        return IndustrialTheme.borderGrey;
    }
  }

  /// 格式化时间
  String _formatTime(DateTime time) {
    final now = DateTime.now();
    final diff = now.difference(time);

    if (diff.inSeconds < 60) {
      return '${diff.inSeconds}秒前';
    } else if (diff.inMinutes < 60) {
      return '${diff.inMinutes}分钟前';
    } else {
      return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
    }
  }
}
